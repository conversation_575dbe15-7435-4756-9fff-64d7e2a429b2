# automation.py
# Logic tự động hóa <PERSON>lenium, t<PERSON><PERSON> bi<PERSON>t <PERSON>

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import NoSuchElementException, WebDriverException
from selenium.webdriver.chrome.service import Service
import time
import os
import sys
import shutil

class AttendanceBot:
    def __init__(self, website_url, log_func=None, headless=True, window_size=(900,700), keep_window_ref=None, stop_flag=None):
        self.website_url = website_url
        self.log_func = log_func
        self.headless = headless
        self.window_size = window_size
        self.keep_window_ref = keep_window_ref if keep_window_ref is not None else []
        self.stop_flag = stop_flag

    def log(self, msg):
        if self.log_func:
            self.log_func(msg)
        print(msg)

    def login_and_attend(self, account):
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        # Thử tối đa 3 lần
        for attempt in range(3):
            if self.stop_flag and self.stop_flag.get('stop'):
                return False, 'Đ<PERSON> dừng theo yêu cầu người dùng'
            try:
                chrome_options = Options()
                if self.headless:
                    chrome_options.add_argument('--headless')
                chrome_options.add_argument('--disable-gpu')
                driver_path = self.get_driver_path()
                service = Service(driver_path)
                driver = webdriver.Chrome(service=service, options=chrome_options)
                driver.set_window_size(self.window_size[0], self.window_size[1])
                if self.keep_window_ref is not None and not self.headless:
                    self.keep_window_ref.append(driver)
                driver.get(self.website_url)
                # Bước 1: Click nút Đăng Nhập để mở form
                WebDriverWait(driver, 10).until(EC.element_to_be_clickable((By.XPATH, '//button[contains(text(), "Đăng Nhập")]'))).click()
                time.sleep(1)
                # Đợi ô username xuất hiện (tối đa 10s)
                WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.CSS_SELECTOR, 'input[placeholder="Tên Đăng Nhập"]')))
                driver.find_element(By.CSS_SELECTOR, 'input[placeholder="Tên Đăng Nhập"]').send_keys(account['username'])
                driver.find_element(By.NAME, 'password').send_keys(account['password'])
                time.sleep(2)
                driver.find_element(By.CSS_SELECTOR, 'button[type="submit"]').click()
                time.sleep(2)
                # Điều hướng tới trang điểm danh
                driver.get(self.website_url + '/event/login')
                time.sleep(2)
                # Cơ chế điểm danh liên tục: lặp qua tất cả các nút "Điểm danh"
                diem_danh_count = 0
                while True:
                    if self.stop_flag and self.stop_flag.get('stop'):
                        driver.quit()
                        return False, 'Đã dừng theo yêu cầu người dùng'
                    diem_danh_btns = driver.find_elements(By.XPATH, '//button[contains(text(), "Điểm danh")]')
                    if not diem_danh_btns:
                        break
                    btn = diem_danh_btns[0]
                    btn.click()
                    time.sleep(1)
                    # Click nút "Chọn Nhân Vật"
                    try:
                        chon_nv_btn = driver.find_element(By.XPATH, '//button[p[contains(text(), "Chọn Nhân Vật")]]')
                        chon_nv_btn.click()
                        time.sleep(1)
                    except Exception as e:
                        self.log('Không tìm thấy nút Chọn Nhân Vật!')
                        break
                    # Chọn nhân vật
                    lis = driver.find_elements(By.XPATH, '//li[.//span[contains(text(), "Tài Khoản: ")]]')
                    found = False
                    for li in lis:
                        if self.stop_flag and self.stop_flag.get('stop'):
                            driver.quit()
                            return False, 'Đã dừng theo yêu cầu người dùng'
                        try:
                            span = li.find_element(By.XPATH, './/span[contains(text(), "Tài Khoản:")]')
                            if account['character'] in span.text:
                                li.click()
                                found = True
                                break
                        except Exception:
                            continue
                    if not found:
                        self.log(f'Không tìm thấy nhân vật {account["character"]}')
                        break
                    time.sleep(1)
                    # Click nút nhận quà
                    nhan_qua_btns = driver.find_elements(By.XPATH, '//button[contains(text(), "Nhận Quà") and contains(@class, "bg-primary-500")]')
                    if not nhan_qua_btns:
                        self.log('Không tìm thấy nút Nhận quà!')
                        break
                    nhan_qua_btns[0].click()
                    time.sleep(1)
                    # Click nút xác nhận nếu có
                    try:
                        xac_nhan_btn = WebDriverWait(driver, 5).until(
                            lambda d: d.find_element(By.ID, 'NXReportButton')
                        )
                        xac_nhan_btn.click()
                        time.sleep(1)
                        diem_danh_count += 1
                    except Exception:
                        self.log('Không tìm thấy nút Xác nhận!')
                        break
                driver.quit()
                if diem_danh_count > 0:
                    return True, f'Đã nhận thưởng {diem_danh_count} ngày liên tiếp'
                else:
                    return False, 'Không có ngày nào để điểm danh'
            except WebDriverException as e:
                self.log(f'Lỗi Selenium: {e}')
                time.sleep(5)
            except Exception as e:
                self.log(f'Lỗi: {e}')
                time.sleep(5)
        return False, 'Điểm danh thất bại sau 3 lần thử'

    def get_driver_path(self):
        if getattr(sys, 'frozen', False):
            exe_dir = sys._MEIPASS
            temp_dir = os.path.dirname(sys.executable)
            src_driver = os.path.join(exe_dir, "chromedriver.exe")
            dst_driver = os.path.join(temp_dir, "chromedriver.exe")
            if not os.path.exists(dst_driver):
                shutil.copy(src_driver, dst_driver)
            return dst_driver
        else:
            return os.path.join(os.path.dirname(__file__), "chromedriver.exe")
