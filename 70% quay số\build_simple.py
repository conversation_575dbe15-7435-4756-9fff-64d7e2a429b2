# build_simple.py - <PERSON>ript build đơn giản

import os
import subprocess

def main():
    print("🚀 Bắt đầu build EXE...")
    
    # Lệnh build đơn giản
    cmd = [
        'pyinstaller', 
        '--onedir',
        '--windowed', 
        '--icon=logo.ico',
        '--name=QuanLyDiemDanh',
        '--exclude-module=PyQt5',
        '--exclude-module=PySide2', 
        '--exclude-module=PySide6',
        '--exclude-module=tkinter',
        '--clean',
        'main.py'
    ]
    
    print("📝 Lệnh:", ' '.join(cmd))
    
    try:
        result = subprocess.run(cmd, check=True)
        print("✅ Build thành công!")
        print("📁 File EXE tại: dist/QuanLyDiemDanh.exe")
        
        # Copy files
        import shutil
        files = ['accounts.json', 'config.json', 'chromedriver.exe']
        for f in files:
            if os.path.exists(f):
                shutil.copy2(f, 'dist/')
                print(f"📋 Copied {f}")
                
    except subprocess.CalledProcessError as e:
        print(f"❌ Build failed: {e}")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == '__main__':
    main()
    input("Press Enter to exit...")
