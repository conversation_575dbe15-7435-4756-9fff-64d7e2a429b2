# main.py
# Ch<PERSON>ơng trình chính: <PERSON><PERSON><PERSON> nối UI và logic tự động hóa

import sys
import json
import os
from datetime import datetime
import threading
from PyQt6.QtWidgets import QApplication, QMessageBox
from uiqt6_simple import AccountManagerUIQt
from automation import AttendanceBot
from wheel import spin_wheel_for_accounts, save_accounts_to_file
from change_password import PasswordChanger

ACCOUNTS_FILE = 'accounts.json'
LOG_FILE = 'log.txt'
CONFIG_FILE = 'config.json'

def load_accounts():
    if not os.path.exists(ACCOUNTS_FILE):
        return []
    with open(ACCOUNTS_FILE, 'r', encoding='utf-8') as f:
        return json.load(f)

def save_accounts(accounts):
    with open(ACCOUNTS_FILE, 'w', encoding='utf-8') as f:
        json.dump(accounts, f, ensure_ascii=False, indent=2)

def load_config():
    if not os.path.exists(CONFIG_FILE):
        return {"website_url": "https://ninjaschoolmobile.com/"}
    with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
        return json.load(f)

def save_config(config):
    with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)

def log_to_file(message):
    with open(LOG_FILE, 'a', encoding='utf-8') as f:
        f.write(message + '\n')

def main():
    accounts = load_accounts()
    config = load_config()
    WEBSITE_URL = config.get('website_url', 'https://ninjaschoolmobile.com/')
    bot = None
    selenium_windows = []
    stop_flag = {'stop': False}
    lock = threading.Lock()

    def on_add(acc):
        accounts.append(acc)
        save_accounts(accounts)
        ui.refresh_table()

    def on_edit(username, new_acc):
        for i, acc in enumerate(accounts):
            if acc['username'] == username:
                accounts[i] = new_acc
                break
        save_accounts(accounts)
        ui.refresh_table()

    def on_delete(username):
        nonlocal accounts
        # Xóa tài khoản khỏi danh sách
        accounts = [acc for acc in accounts if acc['username'] != username]
        save_accounts(accounts)
        # Làm sạch trạng thái liên quan trong UI
        ui.refresh_table()  # Đã làm sạch check_vars, checkboxes, rows
        # Nếu cần, có thể xóa status/log liên quan ở đây
        # (Hiện tại status lưu trong acc, đã xóa acc là xóa luôn status)

    def arrange_selenium_windows():
        # Sắp xếp các cửa sổ selenium theo dạng lưới ngang, lấy kích thước màn hình động
        try:
            import pyautogui
            screen_width, screen_height = pyautogui.size()
        except Exception:
            screen_width, screen_height = 1920, 1080
        n = len(selenium_windows)
        if n == 0:
            return
        win_w, win_h = ui.get_window_size()
        margin = 20
        # Tự động điều chỉnh kích thước cửa sổ nếu quá nhiều cửa sổ
        max_cols = max(1, screen_width // (win_w + margin))
        cols = min(n, max_cols)
        rows = (n + cols - 1) // cols
        # Nếu tổng chiều cao vượt quá màn hình, giảm kích thước cửa sổ
        if rows * (win_h + margin) > screen_height:
            win_h = max(200, (screen_height - margin * (rows + 1)) // rows)
        if cols * (win_w + margin) > screen_width:
            win_w = max(200, (screen_width - margin * (cols + 1)) // cols)
        for i, driver in enumerate(selenium_windows):
            row = i // cols
            col = i % cols
            x = margin + col * (win_w + margin)
            y = margin + row * (win_h + margin)
            try:
                driver.set_window_position(x, y)
                driver.set_window_size(win_w, win_h)
            except Exception:
                pass

    def on_attendance():
        nonlocal bot, selenium_windows
        stop_flag['stop'] = False
        headless = ui.get_headless()
        width, height = ui.get_window_size()
        selected_usernames = ui.get_selected_accounts()
        selenium_windows = []
        website_url = config.get('website_url', '').strip()
        if not website_url:
            ui.log('Vui lòng nhập địa chỉ website trước khi chạy!')
            QMessageBox.warning(ui, 'Thiếu địa chỉ website', 'Vui lòng nhập địa chỉ website trước khi chạy!')
            return
        run_accounts = [acc for acc in accounts if acc['username'] in selected_usernames] if selected_usernames else []
        if not run_accounts:
            ui.log('Vui lòng tích chọn tài khoản để chạy!')
            QMessageBox.warning(ui, 'Chưa chọn tài khoản', 'Vui lòng tích chọn tài khoản để chạy!')
            return
        threads = []
        def run_bot(acc):
            now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            bot = AttendanceBot(website_url, log_func=None, headless=headless, window_size=(width, height), keep_window_ref=selenium_windows, stop_flag=stop_flag)
            success, result = bot.login_and_attend(acc)
            with lock:
                acc['status'] = result
                save_accounts(accounts)
                ui.safe_refresh_table()
            msg = f'[{now}] - Tài khoản: {acc["username"]} - Kết quả: {result}'
            ui.log(msg)
            log_to_file(msg)
        for acc in run_accounts:
            t = threading.Thread(target=run_bot, args=(acc,))
            t.start()
            threads.append(t)
        arrange_selenium_windows()

    def log_callback(msg):
        log_to_file(msg)

    def stop_callback():
        stop_flag['stop'] = True

    def on_url_update(new_url):
        nonlocal WEBSITE_URL
        WEBSITE_URL = new_url
        config['website_url'] = new_url
        save_config(config)

    def on_wheel_spin():
        """Xử lý quay số cho các accounts được chọn"""
        try:
            # Lấy danh sách accounts được chọn
            selected_accounts = []
            for i, checkbox in enumerate(ui.checkboxes):
                if checkbox.isChecked() and i < len(accounts):
                    selected_accounts.append(accounts[i])

            if not selected_accounts:
                QMessageBox.warning(ui, "Cảnh báo", "Vui lòng chọn ít nhất một tài khoản để quay số!")
                return

            # Xác nhận trước khi quay
            reply = QMessageBox.question(ui, "Xác nhận",
                                       f"Bạn có muốn quay số cho {len(selected_accounts)} tài khoản được chọn?",
                                       QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)

            if reply == QMessageBox.StandardButton.Yes:
                # Chạy quay số trong thread riêng
                def run_wheel_spin():
                    try:
                        ui.log_signal.emit(f"🎯 Bắt đầu quay số cho {len(selected_accounts)} tài khoản...")

                        # Lấy settings từ UI
                        headless = ui.get_headless()
                        window_size = ui.get_window_size()
                        use_multithreading = ui.get_multithreading()

                        # Tạo log function để gửi signal
                        def log_func(msg):
                            ui.log_signal.emit(msg)

                        # Import và chạy wheel spinner với parameters từ automation.py
                        from wheel import spin_wheel_for_accounts

                        # Chạy quay số tuần tự (chế độ cũ)
                        def run_sequential():
                            success_count = 0
                            for i, account in enumerate(selected_accounts, 1):
                                if stop_flag['stop']:
                                    ui.log_signal.emit("⏹️ Đã dừng quay số")
                                    break

                                ui.log_signal.emit(f"🎲 [{i}/{len(selected_accounts)}] Đang quay số cho: {account['username']}")

                                # Tạo spinner riêng cho account này
                                from wheel import WheelSpinner
                                spinner = WheelSpinner(WEBSITE_URL, headless, window_size, log_func, stop_flag)

                                try:
                                    if spinner.spin_for_account(account):
                                        success_count += 1
                                        ui.log_signal.emit(f"✅ [{i}/{len(selected_accounts)}] {account['username']}: {account.get('result_quay', 'Không xác định')}")
                                    else:
                                        ui.log_signal.emit(f"❌ [{i}/{len(selected_accounts)}] Quay số thất bại: {account['username']}")

                                    # Lưu kết quả ngay sau mỗi account
                                    save_accounts(accounts)
                                    ui.refresh_signal.emit()
                                finally:
                                    spinner.cleanup()

                            ui.log_signal.emit(f"🎉 Hoàn thành quay số tuần tự: {success_count}/{len(selected_accounts)} thành công")

                        # Chạy quay số đa luồng
                        def run_multithreaded():
                            import concurrent.futures
                            import threading

                            success_count = 0
                            completed_count = 0
                            total_accounts = len(selected_accounts)
                            result_lock = threading.Lock()

                            def process_single_account(account):
                                """Xử lý một account trong thread riêng"""
                                nonlocal success_count, completed_count

                                if stop_flag['stop']:
                                    return False

                                ui.log_signal.emit(f"🎲 Bắt đầu quay số cho: {account['username']}")

                                # Tạo spinner riêng cho account này
                                from wheel import WheelSpinner
                                spinner = WheelSpinner(WEBSITE_URL, headless, window_size, log_func, stop_flag)

                                try:
                                    result = spinner.spin_for_account(account)

                                    with result_lock:
                                        completed_count += 1
                                        if result:
                                            success_count += 1
                                            ui.log_signal.emit(f"✅ [{completed_count}/{total_accounts}] {account['username']}: {account.get('result_quay', 'Không xác định')}")
                                        else:
                                            ui.log_signal.emit(f"❌ [{completed_count}/{total_accounts}] Quay số thất bại: {account['username']}")

                                        # Lưu kết quả ngay sau mỗi account
                                        save_accounts(accounts)
                                        ui.refresh_signal.emit()

                                    return result

                                except Exception as e:
                                    with result_lock:
                                        completed_count += 1
                                        ui.log_signal.emit(f"❌ [{completed_count}/{total_accounts}] Lỗi {account['username']}: {str(e)}")
                                        account['result_quay'] = f"Lỗi: {str(e)}"
                                        save_accounts(accounts)
                                        ui.refresh_signal.emit()
                                    return False
                                finally:
                                    spinner.cleanup()

                            # Chạy đa luồng với ThreadPoolExecutor
                            max_workers = min(len(selected_accounts), 5)  # Tối đa 5 tab cùng lúc
                            ui.log_signal.emit(f"🚀 Bắt đầu quay số đa luồng với {max_workers} tab...")

                            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                                # Submit tất cả tasks
                                future_to_account = {
                                    executor.submit(process_single_account, account): account
                                    for account in selected_accounts
                                }

                                # Đợi tất cả hoàn thành
                                for future in concurrent.futures.as_completed(future_to_account):
                                    if stop_flag['stop']:
                                        ui.log_signal.emit("⏹️ Đang dừng quay số...")
                                        # Cancel các task chưa hoàn thành
                                        for f in future_to_account:
                                            f.cancel()
                                        break

                            ui.log_signal.emit(f"🎉 Hoàn thành quay số đa luồng: {success_count}/{total_accounts} thành công")

                        # Chọn chế độ chạy dựa trên checkbox
                        if use_multithreading and len(selected_accounts) > 1:
                            ui.log_signal.emit(f"🚀 Chế độ đa luồng: {len(selected_accounts)} tài khoản")
                            run_multithreaded()
                        else:
                            ui.log_signal.emit(f"🔄 Chế độ tuần tự: {len(selected_accounts)} tài khoản")
                            run_sequential()

                    except Exception as e:
                        ui.log_signal.emit(f"❌ Lỗi quay số: {str(e)}")

                # Chạy trong thread
                thread = threading.Thread(target=run_wheel_spin, daemon=True)
                thread.start()

        except Exception as e:
            QMessageBox.critical(ui, "Lỗi", f"Lỗi khi quay số: {str(e)}")

    def on_change_password():
        """Xử lý đổi mật khẩu cho các accounts được chọn"""
        try:
            # Lấy danh sách accounts được chọn
            selected_accounts = []
            for i, checkbox in enumerate(ui.checkboxes):
                if checkbox.isChecked() and i < len(accounts):
                    selected_accounts.append(accounts[i])

            if not selected_accounts:
                QMessageBox.warning(ui, "Cảnh báo", "Vui lòng chọn ít nhất một tài khoản để đổi mật khẩu!")
                return

            # Hiển thị dialog nhập mật khẩu mới
            from PyQt6.QtWidgets import QInputDialog, QLineEdit
            new_password, ok = QInputDialog.getText(
                ui,
                "Đổi mật khẩu",
                f"Nhập mật khẩu mới cho {len(selected_accounts)} tài khoản:",
                QLineEdit.EchoMode.Password
            )

            if not ok or not new_password.strip():
                return

            new_password = new_password.strip()

            # Xác nhận mật khẩu
            confirm_password, ok = QInputDialog.getText(
                ui,
                "Xác nhận mật khẩu",
                "Nhập lại mật khẩu mới để xác nhận:",
                QLineEdit.EchoMode.Password
            )

            if not ok or confirm_password != new_password:
                QMessageBox.warning(ui, "Lỗi", "Mật khẩu xác nhận không khớp!")
                return

            # Lưu newpassword vào accounts
            for account in selected_accounts:
                account['newpassword'] = new_password
            save_accounts(accounts)
            ui.refresh_signal.emit()

            # Xác nhận trước khi đổi
            reply = QMessageBox.question(ui, "Xác nhận",
                                       f"Bạn có muốn đổi mật khẩu cho {len(selected_accounts)} tài khoản được chọn?",
                                       QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)

            if reply == QMessageBox.StandardButton.Yes:
                # Chạy đổi mật khẩu trong thread riêng
                def run_change_password():
                    try:
                        ui.log_signal.emit(f"🔐 Bắt đầu đổi mật khẩu cho {len(selected_accounts)} tài khoản...")

                        # Lấy settings từ UI
                        headless = ui.get_headless()
                        window_size = ui.get_window_size()
                        use_multithreading = ui.get_multithreading()

                        # Tạo log function để gửi signal
                        def log_func(msg):
                            ui.log_signal.emit(msg)

                        # Chạy đổi mật khẩu tuần tự
                        def run_sequential_password():
                            success_count = 0
                            for i, account in enumerate(selected_accounts, 1):
                                if stop_flag['stop']:
                                    ui.log_signal.emit("⏹️ Đã dừng đổi mật khẩu")
                                    break

                                ui.log_signal.emit(f"🔐 [{i}/{len(selected_accounts)}] Đang đổi mật khẩu cho: {account['username']}")

                                # Tạo changer riêng cho account này
                                changer = PasswordChanger(WEBSITE_URL, headless, window_size, log_func, stop_flag)

                                try:
                                    if changer.change_password_for_account(account):
                                        success_count += 1
                                        ui.log_signal.emit(f"✅ [{i}/{len(selected_accounts)}] {account['username']}: {account.get('status', 'Thành công')}")
                                    else:
                                        ui.log_signal.emit(f"❌ [{i}/{len(selected_accounts)}] Đổi mật khẩu thất bại: {account['username']}")

                                    # Lưu kết quả ngay sau mỗi account
                                    save_accounts(accounts)
                                    ui.refresh_signal.emit()
                                finally:
                                    changer.cleanup()

                            ui.log_signal.emit(f"🎉 Hoàn thành đổi mật khẩu tuần tự: {success_count}/{len(selected_accounts)} thành công")

                        # Chạy đổi mật khẩu đa luồng
                        def run_multithreaded_password():
                            import concurrent.futures
                            import threading

                            success_count = 0
                            completed_count = 0
                            total_accounts = len(selected_accounts)
                            result_lock = threading.Lock()

                            def process_single_account(account):
                                """Xử lý một account trong thread riêng"""
                                nonlocal success_count, completed_count

                                if stop_flag['stop']:
                                    return False

                                ui.log_signal.emit(f"🔐 Bắt đầu đổi mật khẩu cho: {account['username']}")

                                # Tạo changer riêng cho account này
                                changer = PasswordChanger(WEBSITE_URL, headless, window_size, log_func, stop_flag)

                                try:
                                    result = changer.change_password_for_account(account)

                                    with result_lock:
                                        completed_count += 1
                                        if result:
                                            success_count += 1
                                            ui.log_signal.emit(f"✅ [{completed_count}/{total_accounts}] {account['username']}: {account.get('status', 'Thành công')}")
                                        else:
                                            ui.log_signal.emit(f"❌ [{completed_count}/{total_accounts}] Đổi mật khẩu thất bại: {account['username']}")

                                        # Lưu kết quả ngay sau mỗi account
                                        save_accounts(accounts)
                                        ui.refresh_signal.emit()

                                    return result

                                except Exception as e:
                                    with result_lock:
                                        completed_count += 1
                                        ui.log_signal.emit(f"❌ [{completed_count}/{total_accounts}] Lỗi {account['username']}: {str(e)}")
                                        account['status'] = f"Lỗi: {str(e)}"
                                        save_accounts(accounts)
                                        ui.refresh_signal.emit()
                                    return False
                                finally:
                                    changer.cleanup()

                            # Chạy đa luồng với ThreadPoolExecutor
                            max_workers = min(len(selected_accounts), 5)  # Tối đa 5 tab cùng lúc
                            ui.log_signal.emit(f"🚀 Bắt đầu đổi mật khẩu đa luồng với {max_workers} tab...")

                            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                                # Submit tất cả tasks
                                future_to_account = {
                                    executor.submit(process_single_account, account): account
                                    for account in selected_accounts
                                }

                                # Đợi tất cả hoàn thành
                                for future in concurrent.futures.as_completed(future_to_account):
                                    if stop_flag['stop']:
                                        ui.log_signal.emit("⏹️ Đang dừng đổi mật khẩu...")
                                        # Cancel các task chưa hoàn thành
                                        for f in future_to_account:
                                            f.cancel()
                                        break

                            ui.log_signal.emit(f"🎉 Hoàn thành đổi mật khẩu đa luồng: {success_count}/{total_accounts} thành công")

                        # Chọn chế độ chạy dựa trên checkbox
                        if use_multithreading and len(selected_accounts) > 1:
                            ui.log_signal.emit(f"🚀 Chế độ đa luồng: {len(selected_accounts)} tài khoản")
                            run_multithreaded_password()
                        else:
                            ui.log_signal.emit(f"🔄 Chế độ tuần tự: {len(selected_accounts)} tài khoản")
                            run_sequential_password()

                    except Exception as e:
                        ui.log_signal.emit(f"❌ Lỗi đổi mật khẩu: {str(e)}")

                # Chạy trong thread
                thread = threading.Thread(target=run_change_password, daemon=True)
                thread.start()

        except Exception as e:
            QMessageBox.critical(ui, "Lỗi", f"Lỗi khi đổi mật khẩu: {str(e)}")

    app = QApplication(sys.argv)
    try:
        global ui
        ui = AccountManagerUIQt(accounts, on_add, on_edit, on_delete, on_attendance, log_callback,
                               website_url=WEBSITE_URL, on_url_update=on_url_update, on_wheel_spin=on_wheel_spin, on_change_password=on_change_password)
        ui.set_arrange_callback(arrange_selenium_windows)
        ui.set_stop_callback(stop_callback)
        ui.show()
        app.exec()
    except Exception as e:
        print(f"Lỗi khi chạy ứng dụng: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
