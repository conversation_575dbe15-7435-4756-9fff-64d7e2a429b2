# build_exe.py
# Script tự động build ứng dụng thành EXE

import os
import subprocess
import shutil
import sys

def build_exe():
    """Build ứng dụng thành file EXE"""
    
    print("🚀 Bắt đầu build ứng dụng...")
    
    # Kiểm tra các file cần thiết
    required_files = ['main.py', 'uiqt6_simple.py', 'automation.py', 'logo.ico']
    missing_files = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Thiếu các file: {', '.join(missing_files)}")
        return False
    
    print("✅ Tất cả file cần thiết đã có")
    
    # Xóa thư mục build và dist cũ nếu có
    for folder in ['build', 'dist']:
        if os.path.exists(folder):
            print(f"🗑️ Xóa thư mục {folder} cũ...")
            shutil.rmtree(folder)
    
    # Xóa file .spec cũ nếu có
    if os.path.exists('main.spec'):
        os.remove('main.spec')
        print("🗑️ Xóa file main.spec cũ...")
    
    # Lệnh PyInstaller
    cmd = [
        'pyinstaller',
        '--onefile',                    # Tạo 1 file EXE duy nhất
        '--windowed',                   # Không hiện console
        '--icon=logo.ico',              # Sử dụng logo.ico
        '--name=QuanLyDiemDanh',       # Tên file EXE
        '--add-data=logo.ico;.',       # Thêm logo.ico vào bundle
        '--hidden-import=PyQt6',        # Import PyQt6
        '--hidden-import=selenium',     # Import Selenium
        '--hidden-import=webdriver_manager',  # Import webdriver_manager
        '--clean',                      # Clean cache
        'main.py'                       # File chính
    ]
    
    print("🔨 Đang build với PyInstaller...")
    print(f"📝 Lệnh: {' '.join(cmd)}")
    
    try:
        # Chạy PyInstaller
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Build thành công!")
            
            # Kiểm tra file EXE đã được tạo
            exe_path = os.path.join('dist', 'QuanLyDiemDanh.exe')
            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
                print(f"📦 File EXE: {exe_path}")
                print(f"📏 Kích thước: {file_size:.1f} MB")
                
                # Copy các file cần thiết vào thư mục dist
                files_to_copy = ['accounts.json', 'config.json', 'chromedriver.exe']
                for file in files_to_copy:
                    if os.path.exists(file):
                        shutil.copy2(file, 'dist/')
                        print(f"📋 Đã copy {file} vào dist/")
                
                print("\n🎉 BUILD HOÀN THÀNH!")
                print(f"📁 Thư mục output: {os.path.abspath('dist')}")
                print("📝 Các file trong dist:")
                for file in os.listdir('dist'):
                    print(f"   - {file}")
                
                return True
            else:
                print("❌ Không tìm thấy file EXE sau khi build")
                return False
        else:
            print("❌ Build thất bại!")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Lỗi khi build: {e}")
        return False

def clean_build():
    """Dọn dẹp các file build"""
    print("🧹 Dọn dẹp các file build...")
    
    folders_to_remove = ['build', '__pycache__']
    files_to_remove = ['main.spec']
    
    for folder in folders_to_remove:
        if os.path.exists(folder):
            shutil.rmtree(folder)
            print(f"🗑️ Đã xóa thư mục {folder}")
    
    for file in files_to_remove:
        if os.path.exists(file):
            os.remove(file)
            print(f"🗑️ Đã xóa file {file}")

if __name__ == '__main__':
    print("=" * 50)
    print("🏗️  QUẢN LÝ ĐIỂM DANH - BUILD EXE")
    print("=" * 50)
    
    if build_exe():
        print("\n🎯 Hướng dẫn sử dụng:")
        print("1. Vào thư mục 'dist'")
        print("2. Chạy file 'QuanLyDiemDanh.exe'")
        print("3. Đảm bảo có file 'chromedriver.exe' cùng thư mục")
        
        # Hỏi có muốn dọn dẹp không
        choice = input("\n🧹 Có muốn dọn dẹp các file build tạm? (y/n): ")
        if choice.lower() in ['y', 'yes']:
            clean_build()
    else:
        print("\n❌ Build thất bại. Vui lòng kiểm tra lại!")
    
    input("\nNhấn Enter để thoát...")
