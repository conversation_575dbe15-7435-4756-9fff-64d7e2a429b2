# change_password.py
# Chức năng đổi mật khẩu tự động cho web game

import time
import json
import os
import shutil
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

class PasswordChanger:
    def __init__(self, website_url, headless=True, window_size=(900, 700), log_func=None, stop_flag=None):
        self.website_url = website_url
        self.headless = headless
        self.window_size = window_size
        self.log_func = log_func
        self.stop_flag = stop_flag
        self.driver = None
        
    def log(self, msg):
        """Log message"""
        if self.log_func:
            self.log_func(msg)
        print(msg)
        
    def get_driver_path(self):
        """<PERSON><PERSON><PERSON> đ<PERSON> dẫn chromedriver - sao chép từ automation.py"""
        if os.path.exists('chromedriver.exe'):
            return os.path.abspath('chromedriver.exe')
        
        possible_paths = [
            'chromedriver.exe',
            './chromedriver.exe',
            '../chromedriver.exe',
            'C:/chromedriver/chromedriver.exe',
            'C:/Program Files/chromedriver/chromedriver.exe'
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return os.path.abspath(path)
        
        # Fallback: tìm trong PATH
        chromedriver_path = shutil.which('chromedriver')
        if chromedriver_path:
            return chromedriver_path
            
        raise FileNotFoundError("Không tìm thấy chromedriver.exe")
    
    def hide_chat_widget(self):
        """Ẩn chat widget để tránh click intercepted"""
        try:
            # Tìm và ẩn chat widget iframe
            chat_iframes = self.driver.find_elements(By.CSS_SELECTOR, "iframe[title*='chat']")
            for iframe in chat_iframes:
                self.driver.execute_script("arguments[0].style.display = 'none';", iframe)
            
            # Ẩn các element có thể che phủ
            overlay_elements = self.driver.find_elements(By.CSS_SELECTOR, "[style*='z-index'][style*='position:fixed']")
            for element in overlay_elements:
                try:
                    self.driver.execute_script("arguments[0].style.display = 'none';", element)
                except:
                    pass
                    
            self.log("✅ Đã ẩn chat widget và overlay elements")
        except Exception as e:
            self.log(f"⚠️ Không thể ẩn chat widget: {e}")
    
    def safe_click(self, element):
        """Click an toàn với JavaScript fallback"""
        try:
            # Thử click bình thường trước
            element.click()
            return True
        except Exception as e:
            if "click intercepted" in str(e):
                try:
                    # Fallback: Sử dụng JavaScript click
                    self.driver.execute_script("arguments[0].click();", element)
                    self.log("✅ Đã click bằng JavaScript")
                    return True
                except Exception as js_error:
                    self.log(f"❌ JavaScript click cũng thất bại: {js_error}")
                    return False
            else:
                self.log(f"❌ Click thất bại: {e}")
                return False
    
    def setup_driver_and_login(self, account):
        """Setup driver và login - sử dụng code từ automation.py"""
        try:
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            
            # Setup Chrome options giống automation.py
            chrome_options = Options()
            if self.headless:
                chrome_options.add_argument('--headless')
            chrome_options.add_argument('--disable-gpu')
            
            # Sử dụng driver path từ automation.py
            driver_path = self.get_driver_path()
            service = Service(driver_path)
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # Set window size giống automation.py
            self.driver.set_window_size(self.window_size[0], self.window_size[1])
            
            # Truy cập website
            self.driver.get(self.website_url)
            
            # Ẩn chat widget ngay sau khi load trang
            time.sleep(1)
            self.hide_chat_widget()
            
            # Login process giống automation.py
            # Bước 1: Click nút Đăng Nhập để mở form
            WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, '//button[contains(text(), "Đăng Nhập")]'))
            ).click()
            time.sleep(1)
            
            # Đợi ô username xuất hiện
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, 'input[placeholder="Tên Đăng Nhập"]'))
            )
            
            # Điền thông tin đăng nhập
            self.driver.find_element(By.CSS_SELECTOR, 'input[placeholder="Tên Đăng Nhập"]').send_keys(account['username'])
            self.driver.find_element(By.NAME, 'password').send_keys(account['password'])
            time.sleep(2)
            
            # Click submit
            self.driver.find_element(By.CSS_SELECTOR, 'button[type="submit"]').click()
            time.sleep(2)
            
            self.log(f"✅ Đăng nhập thành công: {account['username']}")
            return True
            
        except Exception as e:
            self.log(f"❌ Lỗi đăng nhập {account['username']}: {e}")
            return False
    
    def navigate_to_profile(self):
        """Truy cập trang profile - thay đổi từ /wheel sang /profile"""
        try:
            # Điều hướng tới trang profile
            profile_url = self.website_url + '/profile'
            self.driver.get(profile_url)
            time.sleep(2)
            
            self.log("✅ Đã truy cập trang profile")
            return True
            
        except Exception as e:
            self.log(f"❌ Lỗi truy cập trang profile: {e}")
            return False
    
    def open_change_password_modal(self):
        """Click nút đổi mật khẩu để mở modal"""
        try:
            # Click nút "Đổi mật khẩu"
            change_password_btn = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "button.break-words.text-sm.text-primary-colors.font-bold.cursor-pointer"))
            )
            
            # Scroll vào view và click an toàn
            self.driver.execute_script("arguments[0].scrollIntoView(true);", change_password_btn)
            time.sleep(0.5)
            
            if self.safe_click(change_password_btn):
                time.sleep(2)  # Đợi modal hiện ra
                self.log("✅ Đã click nút đổi mật khẩu")
                return True
            else:
                self.log("❌ Không thể click nút đổi mật khẩu")
                return False
                
        except Exception as e:
            self.log(f"❌ Lỗi mở modal đổi mật khẩu: {e}")
            return False
    
    def fill_password_form(self, current_password, new_password):
        """Điền form đổi mật khẩu"""
        try:
            # Đợi modal hiện ra hoàn toàn
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, '//input[@placeholder="Mật khẩu hiện tại"]'))
            )
            
            # Điền mật khẩu hiện tại
            current_input = self.driver.find_element(By.XPATH, '//input[@placeholder="Mật khẩu hiện tại"]')
            current_input.clear()
            current_input.send_keys(current_password)
            self.log("✅ Đã điền mật khẩu hiện tại")
            
            # Điền mật khẩu mới
            new_input = self.driver.find_element(By.XPATH, '//input[@placeholder="Mật khẩu mới"]')
            new_input.clear()
            new_input.send_keys(new_password)
            self.log("✅ Đã điền mật khẩu mới")
            
            # Điền xác nhận mật khẩu
            confirm_input = self.driver.find_element(By.XPATH, '//input[@placeholder="Nhập lại mật khẩu"]')
            confirm_input.clear()
            confirm_input.send_keys(new_password)
            self.log("✅ Đã điền xác nhận mật khẩu")
            
            time.sleep(1)
            return True
            
        except Exception as e:
            self.log(f"❌ Lỗi điền form: {e}")
            return False
    
    def submit_password_change(self):
        """Submit form đổi mật khẩu"""
        try:
            # Tìm nút xác nhận
            confirm_button = self.driver.find_element(By.XPATH, '//button[contains(text(), "Xác Nhận")]')
            
            # Remove disabled attribute nếu có
            self.driver.execute_script("arguments[0].removeAttribute('disabled')", confirm_button)
            
            # Click nút xác nhận
            if self.safe_click(confirm_button):
                time.sleep(3)  # Đợi xử lý
                self.log("✅ Đã click nút xác nhận")
                return True
            else:
                self.log("❌ Không thể click nút xác nhận")
                return False
                
        except Exception as e:
            self.log(f"❌ Lỗi submit form: {e}")
            return False
    
    def check_change_result(self):
        """Kiểm tra kết quả đổi mật khẩu"""
        try:
            # Đợi thông báo kết quả
            time.sleep(2)
            
            # Tìm thông báo thành công hoặc lỗi
            success_selectors = [
                "//div[contains(text(), 'thành công')]",
                "//div[contains(text(), 'Thành công')]",
                "//p[contains(text(), 'thành công')]",
                "//span[contains(text(), 'thành công')]"
            ]
            
            error_selectors = [
                "//div[contains(text(), 'lỗi')]",
                "//div[contains(text(), 'Lỗi')]",
                "//div[contains(text(), 'thất bại')]",
                "//p[contains(text(), 'sai')]"
            ]
            
            # Kiểm tra thành công
            for selector in success_selectors:
                try:
                    element = self.driver.find_element(By.XPATH, selector)
                    if element.is_displayed():
                        self.log(f"✅ Đổi mật khẩu thành công: {element.text}")
                        return "Thành công"
                except:
                    continue
            
            # Kiểm tra lỗi
            for selector in error_selectors:
                try:
                    element = self.driver.find_element(By.XPATH, selector)
                    if element.is_displayed():
                        self.log(f"❌ Đổi mật khẩu thất bại: {element.text}")
                        return f"Thất bại: {element.text}"
                except:
                    continue
            
            # Không tìm thấy thông báo rõ ràng
            self.log("⚠️ Không xác định được kết quả đổi mật khẩu")
            return "Không xác định"
            
        except Exception as e:
            self.log(f"❌ Lỗi kiểm tra kết quả: {e}")
            return f"Lỗi: {str(e)}"
    
    def change_password_for_account(self, account):
        """Đổi mật khẩu cho một account"""
        try:
            self.log(f"\n🔐 Bắt đầu đổi mật khẩu cho: {account['username']}")
            
            # Kiểm tra có newpassword không
            if 'newpassword' not in account or not account['newpassword']:
                self.log("❌ Không có mật khẩu mới để đổi")
                return False
            
            # Setup driver và đăng nhập
            if not self.setup_driver_and_login(account):
                return False
            
            # Truy cập trang profile
            if not self.navigate_to_profile():
                return False
            
            # Mở modal đổi mật khẩu
            if not self.open_change_password_modal():
                return False
            
            # Điền form
            if not self.fill_password_form(account['password'], account['newpassword']):
                return False
            
            # Submit form
            if not self.submit_password_change():
                return False
            
            # Kiểm tra kết quả
            result = self.check_change_result()
            
            # Cập nhật account
            if "Thành công" in result:
                # Cập nhật password thành newpassword
                account['password'] = account['newpassword']
                account['status'] = "Đổi mật khẩu thành công"
                self.log(f"🎉 Hoàn thành đổi mật khẩu cho {account['username']}")
                return True
            else:
                account['status'] = f"Đổi mật khẩu thất bại: {result}"
                self.log(f"❌ Đổi mật khẩu thất bại cho {account['username']}: {result}")
                return False
            
        except Exception as e:
            self.log(f"❌ Lỗi đổi mật khẩu cho {account['username']}: {e}")
            account['status'] = f"Lỗi: {str(e)}"
            return False
    
    def cleanup(self):
        """Dọn dẹp driver"""
        if self.driver:
            self.driver.quit()
            self.log("✅ Đã đóng browser")

def save_accounts_to_file(accounts, filename="accounts.json"):
    """Lưu accounts vào file JSON"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(accounts, f, ensure_ascii=False, indent=2)
        print(f"✅ Đã lưu kết quả vào {filename}")
        return True
    except Exception as e:
        print(f"❌ Lỗi lưu file: {e}")
        return False

if __name__ == "__main__":
    # Test với dữ liệu mẫu
    test_accounts = [
        {
            "username": "demo1",
            "password": "oldpass123",
            "newpassword": "newpass456",
            "character": "llvlle"
        }
    ]
    
    website_url = "https://ninjaschoolmobile.com"
    
    # Test đổi mật khẩu
    changer = PasswordChanger(website_url, headless=False)
    
    try:
        for account in test_accounts:
            changer.change_password_for_account(account)
            changer.cleanup()
        
        # Lưu kết quả
        save_accounts_to_file(test_accounts)
        
    except Exception as e:
        print(f"❌ Lỗi chung: {e}")
