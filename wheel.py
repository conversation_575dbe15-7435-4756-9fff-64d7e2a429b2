# wheel.py
# Chứ<PERSON> năng quay số tự động cho web game

import time
import json
import os
import shutil
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

class WheelSpinner:
    def __init__(self, website_url, headless=True, window_size=(900, 700), log_func=None, stop_flag=None):
        self.website_url = website_url
        self.headless = headless
        self.window_size = window_size
        self.log_func = log_func
        self.stop_flag = stop_flag
        self.driver = None

    def log(self, msg):
        """Log message"""
        if self.log_func:
            self.log_func(msg)
        print(msg)

    def get_driver_path(self):
        """<PERSON><PERSON>y đường dẫn chromedriver - sao chép từ automation.py"""
        if os.path.exists('chromedriver.exe'):
            return os.path.abspath('chromedriver.exe')

        possible_paths = [
            'chromedriver.exe',
            './chromedriver.exe',
            '../chromedriver.exe',
            'C:/chromedriver/chromedriver.exe',
            'C:/Program Files/chromedriver/chromedriver.exe'
        ]

        for path in possible_paths:
            if os.path.exists(path):
                return os.path.abspath(path)

        # Fallback: tìm trong PATH
        chromedriver_path = shutil.which('chromedriver')
        if chromedriver_path:
            return chromedriver_path

        raise FileNotFoundError("Không tìm thấy chromedriver.exe")

    def setup_driver_and_login(self, account):
        """Setup driver và login - sử dụng code từ automation.py"""
        try:
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC

            # Setup Chrome options giống automation.py
            chrome_options = Options()
            if self.headless:
                chrome_options.add_argument('--headless')
            chrome_options.add_argument('--disable-gpu')

            # Sử dụng driver path từ automation.py
            driver_path = self.get_driver_path()
            service = Service(driver_path)
            self.driver = webdriver.Chrome(service=service, options=chrome_options)

            # Set window size giống automation.py
            self.driver.set_window_size(self.window_size[0], self.window_size[1])

            # Truy cập website
            self.driver.get(self.website_url)

            # Login process giống automation.py
            # Bước 1: Click nút Đăng Nhập để mở form
            WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, '//button[contains(text(), "Đăng Nhập")]'))
            ).click()
            time.sleep(1)

            # Đợi ô username xuất hiện
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, 'input[placeholder="Tên Đăng Nhập"]'))
            )

            # Điền thông tin đăng nhập
            self.driver.find_element(By.CSS_SELECTOR, 'input[placeholder="Tên Đăng Nhập"]').send_keys(account['username'])
            self.driver.find_element(By.NAME, 'password').send_keys(account['password'])
            time.sleep(2)

            # Click submit
            self.driver.find_element(By.CSS_SELECTOR, 'button[type="submit"]').click()
            time.sleep(2)

            self.log(f"✅ Đăng nhập thành công: {account['username']}")
            return True

        except Exception as e:
            self.log(f"❌ Lỗi đăng nhập {account['username']}: {e}")
            return False

    def navigate_to_wheel(self):
        """Truy cập trang quay số - thay đổi từ /event/login sang /wheel"""
        try:
            # Điều hướng tới trang quay số thay vì trang điểm danh
            wheel_url = self.website_url + '/wheel'
            self.driver.get(wheel_url)
            time.sleep(2)

            self.log("✅ Đã truy cập trang quay số")
            return True

        except Exception as e:
            self.log(f"❌ Lỗi truy cập trang quay số: {e}")
            return False

    def select_character(self, character_name):
        """Chọn nhân vật - sử dụng logic tương tự automation.py"""
        try:
            # Click nút "Chọn Nhân Vật" để mở dropdown
            select_char_btn = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "button p.break-words.truncate"))
            )
            self.log(f"🎯 Click nút chọn nhân vật...")
            select_char_btn.click()

            # Đợi dropdown hiện ra
            time.sleep(1)

            # Tìm tất cả nhân vật trong dropdown - tương tự automation.py
            # Tìm các li elements chứa thông tin nhân vật
            character_elements = WebDriverWait(self.driver, 10).until(
                EC.presence_of_all_elements_located((By.XPATH, "//li[.//span[contains(text(), 'Tài Khoản:')]]"))
            )

            self.log(f"🔍 Tìm thấy {len(character_elements)} nhân vật, đang tìm: {character_name}")

            # So sánh và chọn nhân vật đúng - logic từ automation.py
            found = False
            for li in character_elements:
                if self.stop_flag and self.stop_flag.get('stop'):
                    return False
                try:
                    span = li.find_element(By.XPATH, './/span[contains(text(), "Tài Khoản:")]')
                    if character_name in span.text:
                        li.click()
                        found = True
                        self.log(f"✅ Đã chọn nhân vật: {character_name}")
                        break
                except Exception:
                    continue

            if not found:
                self.log(f"❌ Không tìm thấy nhân vật: {character_name}")
                return False

            time.sleep(1)
            return True

        except Exception as e:
            self.log(f"❌ Lỗi chọn nhân vật: {e}")
            return False

    def select_free_spin(self):
        """Chọn quay miễn phí"""
        try:
            # Click vào option quay miễn phí
            free_spin_option = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "/html/body/div[1]/div[2]/div[4]/main/div/div[1]/div/div[2]/div[1]/div/div[1]/div/div/div"))
            )
            free_spin_option.click()
            self.log("✅ Đã chọn quay miễn phí")
            time.sleep(1)
            return True

        except Exception as e:
            self.log(f"❌ Lỗi chọn quay miễn phí: {e}")
            return False

    def spin_wheel(self):
        """Quay số"""
        try:
            # Click nút "Quay ngay"
            spin_button = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "/html/body/div[1]/div[2]/div[4]/main/div/div[1]/div/div[2]/div[1]/div/div[3]/div[2]/button"))
            )
            spin_button.click()
            self.log("✅ Đã click quay số")
            return True

        except Exception as e:
            self.log(f"❌ Lỗi click quay số: {e}")
            return False

    def get_reward(self):
        """Lấy phần thưởng từ modal - xử lý cả thành công và thất bại"""
        try:
            self.log("⏳ Đợi kết quả quay số (3s)...")
            time.sleep(3)  # Đợi 3s để có kết quả

            # Kiểm tra thông báo lỗi trước (số dư không đủ)
            try:
                error_message = self.driver.find_element(By.CSS_SELECTOR, "p.notiflix-report-message")
                if "Số dư không đủ" in error_message.text:
                    self.log("❌ Số dư không đủ để quay")
                    return "Số dư không đủ để quay"
            except NoSuchElementException:
                pass  # Không có thông báo lỗi, tiếp tục tìm kết quả

            # Tìm kết quả thành công
            try:
                # Tìm phần thưởng trong modal
                reward_element = WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "p.break-words.text-gray-500.dark\\:text-gray-400.font-bold.text-center.leading-5"))
                )
                reward_text = reward_element.text.strip()
                if reward_text:
                    self.log(f"✅ Phần thưởng: {reward_text}")
                    return reward_text
            except TimeoutException:
                pass

            # Fallback: Tìm bất kỳ modal nào có thông tin
            try:
                modal_elements = self.driver.find_elements(By.CSS_SELECTOR, ".modal p, [role='dialog'] p, .popup p")
                for element in modal_elements:
                    text = element.text.strip()
                    # Kiểm tra keywords phần thưởng
                    if any(keyword in text.lower() for keyword in ['đá', 'vàng', 'xu', 'gem', 'coin', 'cấp']):
                        self.log(f"✅ Phần thưởng (fallback): {text}")
                        return text
                    # Kiểm tra thông báo lỗi
                    elif any(keyword in text.lower() for keyword in ['không đủ', 'hết lượt', 'lỗi']):
                        self.log(f"❌ Thông báo: {text}")
                        return text
            except Exception:
                pass

            # Nếu không tìm thấy gì
            self.log("❌ Không tìm thấy kết quả quay số")
            return "Không xác định"

        except Exception as e:
            self.log(f"❌ Lỗi lấy phần thưởng: {e}")
            return f"Lỗi: {str(e)}"

    def close_modal(self):
        """Đóng modal sau khi lấy phần thưởng"""
        try:
            # Tìm nút đóng modal (có thể là X, Close, OK, etc.)
            close_selectors = [
                "button[aria-label='Close']",
                ".modal-close",
                ".close",
                "button:contains('OK')",
                "button:contains('Đóng')",
                "[data-dismiss='modal']"
            ]

            for selector in close_selectors:
                try:
                    close_btn = self.driver.find_element(By.CSS_SELECTOR, selector)
                    close_btn.click()
                    print("✅ Đã đóng modal")
                    return True
                except:
                    continue

            # Nếu không tìm thấy nút đóng, thử ESC
            from selenium.webdriver.common.keys import Keys
            self.driver.find_element(By.TAG_NAME, "body").send_keys(Keys.ESCAPE)
            print("✅ Đã đóng modal bằng ESC")
            return True

        except Exception as e:
            print(f"⚠️ Không thể đóng modal: {e}")
            return False

    def spin_for_account(self, account):
        """Quay số cho một account"""
        try:
            self.log(f"\n🎯 Bắt đầu quay số cho: {account['username']}")

            # Setup driver và đăng nhập
            if not self.setup_driver_and_login(account):
                return False

            # Truy cập trang quay số
            if not self.navigate_to_wheel():
                return False

            # Chọn nhân vật
            if not self.select_character(account['character']):
                return False

            # Chọn quay miễn phí
            if not self.select_free_spin():
                return False

            # Quay số
            if not self.spin_wheel():
                return False

            # Lấy phần thưởng
            reward = self.get_reward()
            account['result_quay'] = reward

            # Đóng modal
            self.close_modal()

            self.log(f"🎉 Hoàn thành quay số cho {account['username']}: {reward}")
            return True

        except Exception as e:
            self.log(f"❌ Lỗi quay số cho {account['username']}: {e}")
            account['result_quay'] = f"Lỗi: {str(e)}"
            return False

    def cleanup(self):
        """Dọn dẹp driver"""
        if self.driver:
            self.driver.quit()
            print("✅ Đã đóng browser")

def spin_wheel_for_accounts(accounts, website_url, headless=True, window_size=(900, 700), log_func=None, stop_flag=None):
    """Quay số cho danh sách accounts"""
    success_count = 0

    try:
        for account in accounts:
            if stop_flag and stop_flag.get('stop'):
                if log_func:
                    log_func("⏹️ Đã dừng quay số theo yêu cầu")
                break

            # Tạo spinner riêng cho mỗi account
            spinner = WheelSpinner(website_url, headless, window_size, log_func, stop_flag)

            try:
                if spinner.spin_for_account(account):
                    success_count += 1
                time.sleep(2)  # Nghỉ giữa các account
            finally:
                spinner.cleanup()  # Đảm bảo cleanup sau mỗi account

        if log_func:
            log_func(f"\n📊 Kết quả: {success_count}/{len(accounts)} accounts thành công")
        else:
            print(f"\n📊 Kết quả: {success_count}/{len(accounts)} accounts thành công")
        return True

    except Exception as e:
        error_msg = f"❌ Lỗi chung: {e}"
        if log_func:
            log_func(error_msg)
        else:
            print(error_msg)
        return False

def save_accounts_to_file(accounts, filename="accounts.json"):
    """Lưu accounts vào file JSON"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(accounts, f, ensure_ascii=False, indent=2)
        print(f"✅ Đã lưu kết quả vào {filename}")
        return True
    except Exception as e:
        print(f"❌ Lỗi lưu file: {e}")
        return False

if __name__ == "__main__":
    # Test với dữ liệu mẫu
    test_accounts = [
        {
            "username": "demo1",
            "password": "passDemo1!",
            "character": "llvlle"
        }
    ]

    website_url = "https://ninjaschoolmobile.com"

    # Chạy quay số
    spin_wheel_for_accounts(test_accounts, website_url, headless=False)

    # Lưu kết quả
    save_accounts_to_file(test_accounts)
