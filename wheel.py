# wheel.py
# Chứ<PERSON> năng quay số tự động cho web game

import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

class WheelSpinner:
    def __init__(self, website_url, headless=True):
        self.website_url = website_url
        self.headless = headless
        self.driver = None
        
    def setup_driver(self):
        """Thiết lập Chrome driver"""
        try:
            chrome_options = Options()
            if self.headless:
                chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.implicitly_wait(10)
            return True
        except Exception as e:
            print(f"Lỗi setup driver: {e}")
            return False
    
    def login(self, username, password):
        """Đăng nhập vào game"""
        try:
            # Truy cập trang login
            self.driver.get(self.website_url)
            
            # Tìm và điền username
            username_field = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.NAME, "username"))
            )
            username_field.clear()
            username_field.send_keys(username)
            
            # Tìm và điền password
            password_field = self.driver.find_element(By.NAME, "password")
            password_field.clear()
            password_field.send_keys(password)
            
            # Click nút login
            login_button = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            login_button.click()
            
            # Đợi login thành công
            WebDriverWait(self.driver, 15).until(
                EC.url_contains("/dashboard")
            )
            
            print(f"✅ Đăng nhập thành công: {username}")
            return True
            
        except TimeoutException:
            print(f"❌ Timeout khi đăng nhập: {username}")
            return False
        except Exception as e:
            print(f"❌ Lỗi đăng nhập {username}: {e}")
            return False
    
    def navigate_to_wheel(self):
        """Truy cập trang quay số"""
        try:
            wheel_url = self.website_url + "/wheel"
            self.driver.get(wheel_url)
            
            # Đợi trang load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            print("✅ Đã truy cập trang quay số")
            return True
            
        except Exception as e:
            print(f"❌ Lỗi truy cập trang quay số: {e}")
            return False
    
    def select_character(self, character_name):
        """Chọn nhân vật"""
        try:
            # Click nút "Chọn Nhân Vật"
            select_char_btn = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "p.break-words.truncate"))
            )
            select_char_btn.click()
            
            # Đợi list nhân vật hiện ra
            time.sleep(2)
            
            # Tìm tất cả nhân vật trong list
            character_elements = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'character-item')]//p")
            
            # So sánh và chọn nhân vật đúng
            for element in character_elements:
                if element.text.strip() == character_name:
                    element.click()
                    print(f"✅ Đã chọn nhân vật: {character_name}")
                    return True
            
            print(f"❌ Không tìm thấy nhân vật: {character_name}")
            return False
            
        except Exception as e:
            print(f"❌ Lỗi chọn nhân vật: {e}")
            return False
    
    def spin_wheel(self):
        """Quay số"""
        try:
            # Click nút "Quay ngay"
            spin_button = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "/html/body/div[1]/div[2]/div[4]/main/div/div[1]/div/div[2]/div[1]/div/div[3]/div[2]/button"))
            )
            spin_button.click()
            
            print("✅ Đã click quay số")
            return True
            
        except Exception as e:
            print(f"❌ Lỗi click quay số: {e}")
            return False
    
    def get_reward(self):
        """Lấy phần thưởng từ modal"""
        try:
            # Đợi modal hiện lên
            modal = WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".modal, [role='dialog'], .popup"))
            )
            
            # Tìm thẻ p chứa phần thưởng
            reward_elements = modal.find_elements(By.TAG_NAME, "p")
            
            for element in reward_elements:
                text = element.text.strip()
                # Kiểm tra nếu text chứa phần thưởng (có thể điều chỉnh logic này)
                if any(keyword in text.lower() for keyword in ['đá', 'vàng', 'xu', 'gem', 'coin']):
                    print(f"✅ Phần thưởng: {text}")
                    return text
            
            # Nếu không tìm thấy theo keywords, lấy text đầu tiên có nội dung
            for element in reward_elements:
                text = element.text.strip()
                if text and len(text) > 3:  # Text có ý nghĩa
                    print(f"✅ Phần thưởng (fallback): {text}")
                    return text
            
            print("❌ Không tìm thấy phần thưởng trong modal")
            return "Không xác định"
            
        except TimeoutException:
            print("❌ Timeout: Modal không hiện lên")
            return "Timeout"
        except Exception as e:
            print(f"❌ Lỗi lấy phần thưởng: {e}")
            return "Lỗi"
    
    def close_modal(self):
        """Đóng modal sau khi lấy phần thưởng"""
        try:
            # Tìm nút đóng modal (có thể là X, Close, OK, etc.)
            close_selectors = [
                "button[aria-label='Close']",
                ".modal-close",
                ".close",
                "button:contains('OK')",
                "button:contains('Đóng')",
                "[data-dismiss='modal']"
            ]
            
            for selector in close_selectors:
                try:
                    close_btn = self.driver.find_element(By.CSS_SELECTOR, selector)
                    close_btn.click()
                    print("✅ Đã đóng modal")
                    return True
                except:
                    continue
            
            # Nếu không tìm thấy nút đóng, thử ESC
            from selenium.webdriver.common.keys import Keys
            self.driver.find_element(By.TAG_NAME, "body").send_keys(Keys.ESCAPE)
            print("✅ Đã đóng modal bằng ESC")
            return True
            
        except Exception as e:
            print(f"⚠️ Không thể đóng modal: {e}")
            return False
    
    def spin_for_account(self, account):
        """Quay số cho một account"""
        try:
            print(f"\n🎯 Bắt đầu quay số cho: {account['username']}")
            
            # Đăng nhập
            if not self.login(account['username'], account['password']):
                return False
            
            # Truy cập trang quay số
            if not self.navigate_to_wheel():
                return False
            
            # Chọn nhân vật
            if not self.select_character(account['character']):
                return False
            
            # Quay số
            if not self.spin_wheel():
                return False
            
            # Lấy phần thưởng
            reward = self.get_reward()
            account['result_quay'] = reward
            
            # Đóng modal
            self.close_modal()
            
            print(f"🎉 Hoàn thành quay số cho {account['username']}: {reward}")
            return True
            
        except Exception as e:
            print(f"❌ Lỗi quay số cho {account['username']}: {e}")
            account['result_quay'] = f"Lỗi: {str(e)}"
            return False
    
    def cleanup(self):
        """Dọn dẹp driver"""
        if self.driver:
            self.driver.quit()
            print("✅ Đã đóng browser")

def spin_wheel_for_accounts(accounts, website_url, headless=True):
    """Quay số cho danh sách accounts"""
    spinner = WheelSpinner(website_url, headless)
    
    try:
        if not spinner.setup_driver():
            return False
        
        success_count = 0
        for account in accounts:
            if spinner.spin_for_account(account):
                success_count += 1
            time.sleep(2)  # Nghỉ giữa các account
        
        print(f"\n📊 Kết quả: {success_count}/{len(accounts)} accounts thành công")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi chung: {e}")
        return False
    finally:
        spinner.cleanup()

def save_accounts_to_file(accounts, filename="accounts.json"):
    """Lưu accounts vào file JSON"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(accounts, f, ensure_ascii=False, indent=2)
        print(f"✅ Đã lưu kết quả vào {filename}")
        return True
    except Exception as e:
        print(f"❌ Lỗi lưu file: {e}")
        return False

if __name__ == "__main__":
    # Test với dữ liệu mẫu
    test_accounts = [
        {
            "username": "demo1",
            "password": "passDemo1!",
            "character": "llvlle"
        }
    ]
    
    website_url = "https://ninjaschoolmobile.com"
    
    # Chạy quay số
    spin_wheel_for_accounts(test_accounts, website_url, headless=False)
    
    # Lưu kết quả
    save_accounts_to_file(test_accounts)
