# wheel.py
# Chứ<PERSON> năng quay số tự động cho web game

import time
import json
import os
import shutil
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

class WheelSpinner:
    def __init__(self, website_url, headless=True, window_size=(900, 700), log_func=None, stop_flag=None):
        self.website_url = website_url
        self.headless = headless
        self.window_size = window_size
        self.log_func = log_func
        self.stop_flag = stop_flag
        self.driver = None

    def log(self, msg):
        """Log message"""
        if self.log_func:
            self.log_func(msg)
        print(msg)

    def get_driver_path(self):
        """<PERSON><PERSON>y đường dẫn chromedriver - sao chép từ automation.py"""
        if os.path.exists('chromedriver.exe'):
            return os.path.abspath('chromedriver.exe')

        possible_paths = [
            'chromedriver.exe',
            './chromedriver.exe',
            '../chromedriver.exe',
            'C:/chromedriver/chromedriver.exe',
            'C:/Program Files/chromedriver/chromedriver.exe'
        ]

        for path in possible_paths:
            if os.path.exists(path):
                return os.path.abspath(path)

        # Fallback: tìm trong PATH
        chromedriver_path = shutil.which('chromedriver')
        if chromedriver_path:
            return chromedriver_path

        raise FileNotFoundError("Không tìm thấy chromedriver.exe")

    def setup_driver_and_login(self, account):
        """Setup driver và login - sử dụng code từ automation.py"""
        try:
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC

            # Setup Chrome options giống automation.py
            chrome_options = Options()
            if self.headless:
                chrome_options.add_argument('--headless')
            chrome_options.add_argument('--disable-gpu')

            # Sử dụng driver path từ automation.py
            driver_path = self.get_driver_path()
            service = Service(driver_path)
            self.driver = webdriver.Chrome(service=service, options=chrome_options)

            # Set window size giống automation.py
            self.driver.set_window_size(self.window_size[0], self.window_size[1])

            # Truy cập website
            self.driver.get(self.website_url)

            # Login process giống automation.py
            # Bước 1: Click nút Đăng Nhập để mở form
            WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, '//button[contains(text(), "Đăng Nhập")]'))
            ).click()
            time.sleep(1)

            # Đợi ô username xuất hiện
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, 'input[placeholder="Tên Đăng Nhập"]'))
            )

            # Điền thông tin đăng nhập
            self.driver.find_element(By.CSS_SELECTOR, 'input[placeholder="Tên Đăng Nhập"]').send_keys(account['username'])
            self.driver.find_element(By.NAME, 'password').send_keys(account['password'])
            time.sleep(2)

            # Click submit
            self.driver.find_element(By.CSS_SELECTOR, 'button[type="submit"]').click()
            time.sleep(2)

            self.log(f"✅ Đăng nhập thành công: {account['username']}")
            return True

        except Exception as e:
            self.log(f"❌ Lỗi đăng nhập {account['username']}: {e}")
            return False

    def navigate_to_wheel(self):
        """Truy cập trang quay số - thay đổi từ /event/login sang /wheel"""
        try:
            # Điều hướng tới trang quay số thay vì trang điểm danh
            wheel_url = self.website_url + '/wheel'
            self.driver.get(wheel_url)
            time.sleep(2)

            self.log("✅ Đã truy cập trang quay số")
            return True

        except Exception as e:
            self.log(f"❌ Lỗi truy cập trang quay số: {e}")
            return False

    def select_character(self, character_name):
        """Chọn nhân vật"""
        try:
            # Click nút "Chọn Nhân Vật"
            select_char_btn = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "p.break-words.truncate"))
            )
            select_char_btn.click()

            # Đợi list nhân vật hiện ra
            time.sleep(2)

            # Tìm tất cả nhân vật trong list
            character_elements = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'character-item')]//p")

            # So sánh và chọn nhân vật đúng
            for element in character_elements:
                if element.text.strip() == character_name:
                    element.click()
                    print(f"✅ Đã chọn nhân vật: {character_name}")
                    return True

            print(f"❌ Không tìm thấy nhân vật: {character_name}")
            return False

        except Exception as e:
            print(f"❌ Lỗi chọn nhân vật: {e}")
            return False

    def spin_wheel(self):
        """Quay số"""
        try:
            # Click nút "Quay ngay"
            spin_button = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "/html/body/div[1]/div[2]/div[4]/main/div/div[1]/div/div[2]/div[1]/div/div[3]/div[2]/button"))
            )
            spin_button.click()

            print("✅ Đã click quay số")
            return True

        except Exception as e:
            print(f"❌ Lỗi click quay số: {e}")
            return False

    def get_reward(self):
        """Lấy phần thưởng từ modal"""
        try:
            # Đợi modal hiện lên
            modal = WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".modal, [role='dialog'], .popup"))
            )

            # Tìm thẻ p chứa phần thưởng
            reward_elements = modal.find_elements(By.TAG_NAME, "p")

            for element in reward_elements:
                text = element.text.strip()
                # Kiểm tra nếu text chứa phần thưởng (có thể điều chỉnh logic này)
                if any(keyword in text.lower() for keyword in ['đá', 'vàng', 'xu', 'gem', 'coin']):
                    print(f"✅ Phần thưởng: {text}")
                    return text

            # Nếu không tìm thấy theo keywords, lấy text đầu tiên có nội dung
            for element in reward_elements:
                text = element.text.strip()
                if text and len(text) > 3:  # Text có ý nghĩa
                    print(f"✅ Phần thưởng (fallback): {text}")
                    return text

            print("❌ Không tìm thấy phần thưởng trong modal")
            return "Không xác định"

        except TimeoutException:
            print("❌ Timeout: Modal không hiện lên")
            return "Timeout"
        except Exception as e:
            print(f"❌ Lỗi lấy phần thưởng: {e}")
            return "Lỗi"

    def close_modal(self):
        """Đóng modal sau khi lấy phần thưởng"""
        try:
            # Tìm nút đóng modal (có thể là X, Close, OK, etc.)
            close_selectors = [
                "button[aria-label='Close']",
                ".modal-close",
                ".close",
                "button:contains('OK')",
                "button:contains('Đóng')",
                "[data-dismiss='modal']"
            ]

            for selector in close_selectors:
                try:
                    close_btn = self.driver.find_element(By.CSS_SELECTOR, selector)
                    close_btn.click()
                    print("✅ Đã đóng modal")
                    return True
                except:
                    continue

            # Nếu không tìm thấy nút đóng, thử ESC
            from selenium.webdriver.common.keys import Keys
            self.driver.find_element(By.TAG_NAME, "body").send_keys(Keys.ESCAPE)
            print("✅ Đã đóng modal bằng ESC")
            return True

        except Exception as e:
            print(f"⚠️ Không thể đóng modal: {e}")
            return False

    def spin_for_account(self, account):
        """Quay số cho một account"""
        try:
            self.log(f"\n🎯 Bắt đầu quay số cho: {account['username']}")

            # Setup driver và đăng nhập
            if not self.setup_driver_and_login(account):
                return False

            # Truy cập trang quay số
            if not self.navigate_to_wheel():
                return False

            # Chọn nhân vật
            if not self.select_character(account['character']):
                return False

            # Quay số
            if not self.spin_wheel():
                return False

            # Lấy phần thưởng
            reward = self.get_reward()
            account['result_quay'] = reward

            # Đóng modal
            self.close_modal()

            self.log(f"🎉 Hoàn thành quay số cho {account['username']}: {reward}")
            return True

        except Exception as e:
            self.log(f"❌ Lỗi quay số cho {account['username']}: {e}")
            account['result_quay'] = f"Lỗi: {str(e)}"
            return False

    def cleanup(self):
        """Dọn dẹp driver"""
        if self.driver:
            self.driver.quit()
            print("✅ Đã đóng browser")

def spin_wheel_for_accounts(accounts, website_url, headless=True, window_size=(900, 700), log_func=None, stop_flag=None):
    """Quay số cho danh sách accounts"""
    success_count = 0

    try:
        for account in accounts:
            if stop_flag and stop_flag.get('stop'):
                if log_func:
                    log_func("⏹️ Đã dừng quay số theo yêu cầu")
                break

            # Tạo spinner riêng cho mỗi account
            spinner = WheelSpinner(website_url, headless, window_size, log_func, stop_flag)

            try:
                if spinner.spin_for_account(account):
                    success_count += 1
                time.sleep(2)  # Nghỉ giữa các account
            finally:
                spinner.cleanup()  # Đảm bảo cleanup sau mỗi account

        if log_func:
            log_func(f"\n📊 Kết quả: {success_count}/{len(accounts)} accounts thành công")
        else:
            print(f"\n📊 Kết quả: {success_count}/{len(accounts)} accounts thành công")
        return True

    except Exception as e:
        error_msg = f"❌ Lỗi chung: {e}"
        if log_func:
            log_func(error_msg)
        else:
            print(error_msg)
        return False

def save_accounts_to_file(accounts, filename="accounts.json"):
    """Lưu accounts vào file JSON"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(accounts, f, ensure_ascii=False, indent=2)
        print(f"✅ Đã lưu kết quả vào {filename}")
        return True
    except Exception as e:
        print(f"❌ Lỗi lưu file: {e}")
        return False

if __name__ == "__main__":
    # Test với dữ liệu mẫu
    test_accounts = [
        {
            "username": "demo1",
            "password": "passDemo1!",
            "character": "llvlle"
        }
    ]

    website_url = "https://ninjaschoolmobile.com"

    # Chạy quay số
    spin_wheel_for_accounts(test_accounts, website_url, headless=False)

    # Lưu kết quả
    save_accounts_to_file(test_accounts)
