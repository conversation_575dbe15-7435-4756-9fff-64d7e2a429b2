# wheel.py
# Chứ<PERSON> năng quay số tự động cho web game

import time
import json
import os
import shutil
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

class WheelSpinner:
    def __init__(self, website_url, headless=True, window_size=(900, 700), log_func=None, stop_flag=None):
        self.website_url = website_url
        self.headless = headless
        self.window_size = window_size
        self.log_func = log_func
        self.stop_flag = stop_flag
        self.driver = None

    def log(self, msg):
        """Log message"""
        if self.log_func:
            self.log_func(msg)
        print(msg)

    def get_driver_path(self):
        """<PERSON><PERSON>y đường dẫn chromedriver - sao chép từ automation.py"""
        if os.path.exists('chromedriver.exe'):
            return os.path.abspath('chromedriver.exe')

        possible_paths = [
            'chromedriver.exe',
            './chromedriver.exe',
            '../chromedriver.exe',
            'C:/chromedriver/chromedriver.exe',
            'C:/Program Files/chromedriver/chromedriver.exe'
        ]

        for path in possible_paths:
            if os.path.exists(path):
                return os.path.abspath(path)

        # Fallback: tìm trong PATH
        chromedriver_path = shutil.which('chromedriver')
        if chromedriver_path:
            return chromedriver_path

        raise FileNotFoundError("Không tìm thấy chromedriver.exe")

    def setup_driver_and_login(self, account):
        """Setup driver và login - sử dụng code từ automation.py"""
        try:
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC

            # Setup Chrome options giống automation.py
            chrome_options = Options()
            if self.headless:
                chrome_options.add_argument('--headless')
            chrome_options.add_argument('--disable-gpu')

            # Sử dụng driver path từ automation.py
            driver_path = self.get_driver_path()
            service = Service(driver_path)
            self.driver = webdriver.Chrome(service=service, options=chrome_options)

            # Set window size giống automation.py
            self.driver.set_window_size(self.window_size[0], self.window_size[1])

            # Truy cập website
            self.driver.get(self.website_url)

            # Ẩn chat widget ngay sau khi load trang
            time.sleep(1)
            self.hide_chat_widget()

            # Login process giống automation.py
            # Bước 1: Click nút Đăng Nhập để mở form
            WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, '//button[contains(text(), "Đăng Nhập")]'))
            ).click()
            time.sleep(1)

            # Đợi ô username xuất hiện
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, 'input[placeholder="Tên Đăng Nhập"]'))
            )

            # Điền thông tin đăng nhập
            self.driver.find_element(By.CSS_SELECTOR, 'input[placeholder="Tên Đăng Nhập"]').send_keys(account['username'])
            self.driver.find_element(By.NAME, 'password').send_keys(account['password'])
            time.sleep(2)

            # Click submit
            self.driver.find_element(By.CSS_SELECTOR, 'button[type="submit"]').click()
            time.sleep(2)

            self.log(f"✅ Đăng nhập thành công: {account['username']}")
            return True

        except Exception as e:
            self.log(f"❌ Lỗi đăng nhập {account['username']}: {e}")
            return False

    def navigate_to_wheel(self):
        """Truy cập trang quay số - thay đổi từ /event/login sang /wheel"""
        try:
            # Điều hướng tới trang quay số thay vì trang điểm danh
            wheel_url = self.website_url + '/wheel'
            self.driver.get(wheel_url)
            time.sleep(2)

            self.log("✅ Đã truy cập trang quay số")
            return True

        except Exception as e:
            self.log(f"❌ Lỗi truy cập trang quay số: {e}")
            return False

    def select_character(self, character_name):
        """Chọn nhân vật - sử dụng logic tương tự automation.py"""
        try:
            # Click nút "Chọn Nhân Vật" để mở dropdown
            select_char_btn = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "button p.break-words.truncate"))
            )
            self.log(f"🎯 Click nút chọn nhân vật...")
            select_char_btn.click()

            # Đợi dropdown hiện ra
            time.sleep(1)

            # Tìm tất cả nhân vật trong dropdown - tương tự automation.py
            # Tìm các li elements chứa thông tin nhân vật
            character_elements = WebDriverWait(self.driver, 10).until(
                EC.presence_of_all_elements_located((By.XPATH, "//li[.//span[contains(text(), 'Tài Khoản:')]]"))
            )

            self.log(f"🔍 Tìm thấy {len(character_elements)} nhân vật, đang tìm: {character_name}")

            # So sánh và chọn nhân vật đúng - logic từ automation.py
            found = False
            for li in character_elements:
                if self.stop_flag and self.stop_flag.get('stop'):
                    return False
                try:
                    span = li.find_element(By.XPATH, './/span[contains(text(), "Tài Khoản:")]')
                    if character_name in span.text:
                        li.click()
                        found = True
                        self.log(f"✅ Đã chọn nhân vật: {character_name}")
                        break
                except Exception:
                    continue

            if not found:
                self.log(f"❌ Không tìm thấy nhân vật: {character_name}")
                return False

            time.sleep(1)
            return True

        except Exception as e:
            self.log(f"❌ Lỗi chọn nhân vật: {e}")
            return False

    def select_free_spin(self):
        """Chọn quay miễn phí"""
        try:
            # Click vào option quay miễn phí
            free_spin_option = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "/html/body/div[1]/div[2]/div[4]/main/div/div[1]/div/div[2]/div[1]/div/div[1]/div/div/div"))
            )
            free_spin_option.click()
            self.log("✅ Đã chọn quay miễn phí")
            time.sleep(1)
            return True

        except Exception as e:
            self.log(f"❌ Lỗi chọn quay miễn phí: {e}")
            return False

    def spin_wheel(self):
        """Quay số"""
        try:
            # Click nút "Quay ngay"
            spin_button = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "/html/body/div[1]/div[2]/div[4]/main/div/div[1]/div/div[2]/div[1]/div/div[3]/div[2]/button"))
            )
            spin_button.click()
            self.log("✅ Đã click quay số")
            return True

        except Exception as e:
            self.log(f"❌ Lỗi click quay số: {e}")
            return False

    def is_valid_reward(self, text):
        """Kiểm tra xem text có phải là phần thưởng hợp lệ không"""
        # Danh sách phần thưởng có thể có trong quay số
        valid_rewards = [
            "Bảo hiểm như ý",
            "Phiếu may mắn",
            "Linh Chi Vạn Năm",
            "Đá cấp 9",
            "Đá danh vọng cấp 1",
            "Đá cấp 10",
            "Bát bảo",
            "Rương bạch ngân",
            "Rương Huyền bí",
            "Chuyển tinh thạch"
        ]

        # Kiểm tra exact match hoặc partial match
        text_lower = text.lower()
        for reward in valid_rewards:
            if reward.lower() in text_lower or text_lower in reward.lower():
                return True

        # Kiểm tra pattern với số lượng (ví dụ: "Đá cấp 10 x1")
        for reward in valid_rewards:
            if reward.lower() in text_lower and any(char in text for char in ['x', '×', '*']):
                return True

        return False

    def get_reward(self):
        """Lấy phần thưởng từ modal - xử lý cả thành công và thất bại"""
        try:
            self.log("⏳ Đợi kết quả quay số (10s)...")
            time.sleep(10)  # Đợi 3s để có kết quả

            # Kiểm tra thông báo lỗi trước (số dư không đủ)
            try:
                error_message = self.driver.find_element(By.CSS_SELECTOR, "p.notiflix-report-message")
                if "Số dư không đủ" in error_message.text:
                    self.log("❌ Số dư không đủ để quay")
                    return "Số dư không đủ để quay"
            except NoSuchElementException:
                pass  # Không có thông báo lỗi, tiếp tục tìm kết quả

            # Tìm kết quả thành công
            try:
                # Tìm phần thưởng trong modal
                reward_element = WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "p.break-words.text-gray-500.dark\\:text-gray-400.font-bold.text-center.leading-5"))
                )
                reward_text = reward_element.text.strip()
                if reward_text and self.is_valid_reward(reward_text):
                    self.log(f"✅ Phần thưởng: {reward_text}")
                    return reward_text
                elif reward_text:
                    # Nếu có text nhưng không match danh sách, vẫn trả về
                    self.log(f"✅ Phần thưởng (không trong danh sách): {reward_text}")
                    return reward_text
            except TimeoutException:
                pass

            # Fallback: Tìm bất kỳ modal nào có thông tin
            try:
                modal_elements = self.driver.find_elements(By.CSS_SELECTOR, ".modal p, [role='dialog'] p, .popup p")
                for element in modal_elements:
                    text = element.text.strip()
                    # Kiểm tra danh sách phần thưởng cụ thể
                    if self.is_valid_reward(text):
                        self.log(f"✅ Phần thưởng (fallback): {text}")
                        return text
                    # Kiểm tra thông báo lỗi
                    elif any(keyword in text.lower() for keyword in ['không đủ', 'hết lượt', 'lỗi']):
                        self.log(f"❌ Thông báo: {text}")
                        return text
            except Exception:
                pass

            # Nếu không tìm thấy gì, thử lấy từ lịch sử
            self.log("⚠️ Không tìm thấy kết quả trực tiếp, thử lấy từ lịch sử...")
            history_result = self.get_reward_from_history()
            if history_result != "Không xác định":
                return history_result

            # Nếu vẫn không tìm thấy gì
            self.log("❌ Không tìm thấy kết quả quay số")
            return "Không xác định"

        except Exception as e:
            self.log(f"❌ Lỗi lấy phần thưởng: {e}")
            return f"Lỗi: {str(e)}"

    def hide_chat_widget(self):
        """Ẩn chat widget để tránh click intercepted"""
        try:
            # Tìm và ẩn chat widget iframe
            chat_iframes = self.driver.find_elements(By.CSS_SELECTOR, "iframe[title*='chat']")
            for iframe in chat_iframes:
                self.driver.execute_script("arguments[0].style.display = 'none';", iframe)

            # Ẩn các element có thể che phủ
            overlay_elements = self.driver.find_elements(By.CSS_SELECTOR, "[style*='z-index'][style*='position:fixed']")
            for element in overlay_elements:
                try:
                    self.driver.execute_script("arguments[0].style.display = 'none';", element)
                except:
                    pass

            self.log("✅ Đã ẩn chat widget và overlay elements")
        except Exception as e:
            self.log(f"⚠️ Không thể ẩn chat widget: {e}")

    def safe_click(self, element):
        """Click an toàn với JavaScript fallback"""
        try:
            # Thử click bình thường trước
            element.click()
            return True
        except Exception as e:
            if "click intercepted" in str(e):
                try:
                    # Fallback: Sử dụng JavaScript click
                    self.driver.execute_script("arguments[0].click();", element)
                    self.log("✅ Đã click bằng JavaScript")
                    return True
                except Exception as js_error:
                    self.log(f"❌ JavaScript click cũng thất bại: {js_error}")
                    return False
            else:
                self.log(f"❌ Click thất bại: {e}")
                return False

    def get_reward_from_history(self):
        """Lấy kết quả từ lịch sử quay số - fallback method"""
        try:
            self.log("🔄 Reload trang /wheel để truy cập lịch sử...")

            # 1. Reload trang /wheel
            wheel_url = self.website_url + '/wheel'
            self.driver.get(wheel_url)
            time.sleep(2)

            # 2. Ẩn chat widget ngay sau khi load
            self.hide_chat_widget()

            # 3. Chọn lại nhân vật (cần để truy cập được lịch sử)
            self.log("👤 Chọn lại nhân vật để truy cập lịch sử...")
            try:
                select_char_btn = WebDriverWait(self.driver, 10).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, "button p.break-words.truncate"))
                )
                if self.safe_click(select_char_btn):
                    time.sleep(1)

                    # Click vào nhân vật đầu tiên
                    character_elements = self.driver.find_elements(By.XPATH, "//li[.//span[contains(text(), 'Tài Khoản:')]]")
                    if character_elements:
                        if self.safe_click(character_elements[0]):
                            time.sleep(1)
                            self.log("✅ Đã chọn lại nhân vật")
            except Exception as e:
                self.log(f"⚠️ Không thể chọn lại nhân vật: {e}")

            # 4. Click vào nút lịch sử
            self.log("📋 Truy cập lịch sử quay số...")
            try:
                history_btn = WebDriverWait(self.driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, "/html/body/div[1]/div[2]/div[4]/main/div/div[1]/div/div[2]/div[2]/div/div/div[1]/div/div[1]/button"))
                )
                if self.safe_click(history_btn):
                    time.sleep(1)
                    self.log("✅ Đã click nút lịch sử")
                else:
                    self.log("❌ Không thể click nút lịch sử")
                    return "Không xác định"
            except Exception as e:
                self.log(f"❌ Không tìm thấy nút lịch sử: {e}")
                return "Không xác định"

            # 5. Click vào dropdown số lượng hiển thị (li[5] = 100 records)
            self.log("📊 Chọn hiển thị 100 records...")
            try:
                # Thử nhiều selector khác nhau
                selectors = [
                    "/html/body/div[1]/div[2]/div[4]/main/div/div[1]/div/div[2]/div[2]/div/div/div[1]/div/div[2]/ul/li[5]/div/span",
                    "//li[contains(text(), '100')]//span",
                    "//span[text()='100']",
                    "//li[5]//span[contains(@class, 'truncate')]"
                ]

                dropdown_option = None
                for selector in selectors:
                    try:
                        if selector.startswith("//"):
                            dropdown_option = WebDriverWait(self.driver, 5).until(
                                EC.element_to_be_clickable((By.XPATH, selector))
                            )
                        else:
                            dropdown_option = WebDriverWait(self.driver, 5).until(
                                EC.element_to_be_clickable((By.XPATH, selector))
                            )
                        self.log(f"✅ Tìm thấy dropdown với selector: {selector}")
                        break
                    except:
                        continue

                if dropdown_option:
                    # Scroll element vào view trước khi click
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", dropdown_option)
                    time.sleep(0.5)

                    if self.safe_click(dropdown_option):
                        time.sleep(2)  # Đợi table load
                        self.log("✅ Đã chọn hiển thị 100 records")
                    else:
                        self.log("❌ Không thể chọn 100 records")
                        return "Không xác định"
                else:
                    self.log("❌ Không tìm thấy dropdown option với tất cả selectors")
                    return "Không xác định"

            except Exception as e:
                self.log(f"❌ Lỗi khi chọn dropdown: {e}")
                return "Không xác định"

            # 5. Lấy kết quả từ dòng đầu tiên trong bảng lịch sử
            self.log("🔍 Tìm kết quả mới nhất trong lịch sử...")
            try:
                # Tìm dòng đầu tiên trong tbody
                first_row = WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "tbody tr:first-child"))
                )

                # Lấy cột "Phần Thưởng" (cột thứ 2)
                reward_cell = first_row.find_element(By.CSS_SELECTOR, "td:nth-child(2)")

                # Tìm text phần thưởng trong cell
                reward_span = reward_cell.find_element(By.CSS_SELECTOR, "div span")
                reward_text = reward_span.text.strip()

                if reward_text and self.is_valid_reward(reward_text):
                    self.log(f"✅ Tìm thấy trong lịch sử: {reward_text}")
                    return reward_text
                elif reward_text:
                    self.log(f"✅ Tìm thấy trong lịch sử (không trong danh sách): {reward_text}")
                    return reward_text

            except Exception as e:
                self.log(f"⚠️ Không thể đọc dữ liệu từ bảng lịch sử: {e}")

            # 6. Fallback: Tìm bất kỳ text nào có chứa phần thưởng
            try:
                reward_elements = self.driver.find_elements(By.CSS_SELECTOR, "tbody td:nth-child(2) span")
                for element in reward_elements:
                    text = element.text.strip()
                    if text and self.is_valid_reward(text):
                        self.log(f"✅ Tìm thấy trong lịch sử (fallback): {text}")
                        return text
            except Exception as e:
                self.log(f"⚠️ Fallback search failed: {e}")

            self.log("❌ Không tìm thấy kết quả trong lịch sử")
            return "Không xác định"

        except Exception as e:
            self.log(f"❌ Lỗi truy cập lịch sử: {e}")
            return "Không xác định"

    def close_modal(self):
        """Đóng modal sau khi lấy phần thưởng"""
        try:
            # Tìm nút đóng modal (có thể là X, Close, OK, etc.)
            close_selectors = [
                "button[aria-label='Close']",
                ".modal-close",
                ".close",
                "button:contains('OK')",
                "button:contains('Đóng')",
                "[data-dismiss='modal']"
            ]

            for selector in close_selectors:
                try:
                    close_btn = self.driver.find_element(By.CSS_SELECTOR, selector)
                    close_btn.click()
                    print("✅ Đã đóng modal")
                    return True
                except:
                    continue

            # Nếu không tìm thấy nút đóng, thử ESC
            from selenium.webdriver.common.keys import Keys
            self.driver.find_element(By.TAG_NAME, "body").send_keys(Keys.ESCAPE)
            print("✅ Đã đóng modal bằng ESC")
            return True

        except Exception as e:
            print(f"⚠️ Không thể đóng modal: {e}")
            return False

    def spin_for_account(self, account):
        """Quay số cho một account"""
        try:
            self.log(f"\n🎯 Bắt đầu quay số cho: {account['username']}")

            # Setup driver và đăng nhập
            if not self.setup_driver_and_login(account):
                return False

            # Truy cập trang quay số
            if not self.navigate_to_wheel():
                return False

            # Chọn nhân vật
            if not self.select_character(account['character']):
                return False

            # Chọn quay miễn phí
            if not self.select_free_spin():
                return False

            # Quay số
            if not self.spin_wheel():
                return False

            # Lấy phần thưởng
            reward = self.get_reward()
            account['result_quay'] = reward

            # Đóng modal
            self.close_modal()

            self.log(f"🎉 Hoàn thành quay số cho {account['username']}: {reward}")
            return True

        except Exception as e:
            self.log(f"❌ Lỗi quay số cho {account['username']}: {e}")
            account['result_quay'] = f"Lỗi: {str(e)}"
            return False

    def cleanup(self):
        """Dọn dẹp driver"""
        if self.driver:
            self.driver.quit()
            print("✅ Đã đóng browser")

def spin_wheel_for_accounts(accounts, website_url, headless=True, window_size=(900, 700), log_func=None, stop_flag=None):
    """Quay số cho danh sách accounts"""
    success_count = 0

    try:
        for account in accounts:
            if stop_flag and stop_flag.get('stop'):
                if log_func:
                    log_func("⏹️ Đã dừng quay số theo yêu cầu")
                break

            # Tạo spinner riêng cho mỗi account
            spinner = WheelSpinner(website_url, headless, window_size, log_func, stop_flag)

            try:
                if spinner.spin_for_account(account):
                    success_count += 1
                time.sleep(2)  # Nghỉ giữa các account
            finally:
                spinner.cleanup()  # Đảm bảo cleanup sau mỗi account

        if log_func:
            log_func(f"\n📊 Kết quả: {success_count}/{len(accounts)} accounts thành công")
        else:
            print(f"\n📊 Kết quả: {success_count}/{len(accounts)} accounts thành công")
        return True

    except Exception as e:
        error_msg = f"❌ Lỗi chung: {e}"
        if log_func:
            log_func(error_msg)
        else:
            print(error_msg)
        return False

def save_accounts_to_file(accounts, filename="accounts.json"):
    """Lưu accounts vào file JSON"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(accounts, f, ensure_ascii=False, indent=2)
        print(f"✅ Đã lưu kết quả vào {filename}")
        return True
    except Exception as e:
        print(f"❌ Lỗi lưu file: {e}")
        return False

if __name__ == "__main__":
    # Test với dữ liệu mẫu
    test_accounts = [
        {
            "username": "demo1",
            "password": "passDemo1!",
            "character": "llvlle"
        }
    ]

    website_url = "https://ninjaschoolmobile.com"

    # Chạy quay số
    spin_wheel_for_accounts(test_accounts, website_url, headless=False)

    # Lưu kết quả
    save_accounts_to_file(test_accounts)
