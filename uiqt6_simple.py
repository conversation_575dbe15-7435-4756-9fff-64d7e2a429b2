# uiqt6_simple.py
# Giao diện PyQt6 đơ<PERSON>, tối ưu cho khả năng hiển thị trên mọi máy

from PyQt6.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                             QLineEdit, QTableWidget, QTableWidgetItem, QHeaderView, QCheckBox,
                             QTextEdit, QAbstractItemView, QDialog, QFrame, QSizePolicy, QMessageBox)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QObject
from PyQt6.QtGui import QIcon, QColor
import sys
import os
import json
from datetime import datetime

class AccountManagerUIQt(QWidget):
    # Signals để xử lý thread-safe UI updates
    log_signal = pyqtSignal(str)
    refresh_signal = pyqtSignal()

    def __init__(self, accounts, on_add, on_edit, on_delete, on_attendance, log_callback, website_url, on_url_update):
        super().__init__()
        self.accounts = accounts
        self.on_add = on_add
        self.on_edit = on_edit
        self.on_delete = on_delete
        self.on_attendance = on_attendance
        self.log_callback = log_callback
        self.on_arrange = lambda: None  # Khởi tạo mặc định tránh lỗi
        self.website_url = website_url
        self.on_url_update = on_url_update
        self.on_stop = lambda: None  # Khởi tạo mặc định

        # Biến lưu trữ checkbox states
        self.check_vars = []
        self.checkboxes = []
        self.rows = []

        # Kết nối signals
        self.log_signal.connect(self._safe_log)
        self.refresh_signal.connect(self._safe_refresh)

        self.setWindowTitle('Quản lý điểm danh tự động (PyQt6)')
        self.setMinimumSize(1200, 750)  # Giảm thêm kích thước tối thiểu
        self.resize(950, 750)  # Kích thước mặc định nhỏ hơn
        self.init_ui()

    def init_ui(self):
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 10, 15, 10)  # Giảm margins
        main_layout.setSpacing(10)  # Giảm spacing
        self.setStyleSheet('''
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                font-family: "Segoe UI", "Roboto", "Arial", sans-serif;
            }
        ''')

        # Header responsive
        header_container = QWidget()
        header_container.setFixedHeight(60)  # Giảm chiều cao
        header_container.setStyleSheet('''
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 10px;
                margin: 2px;
            }
        ''')
        header_layout = QHBoxLayout(header_container)
        header_layout.setContentsMargins(15, 0, 15, 0)  # Giảm margins

        # Title đơn giản
        header = QLabel('QUẢN LÝ ĐIỂM DANH TỰ ĐỘNG')
        header.setStyleSheet('''
            color: white;
            font-size: 22px;
            font-weight: 700;
            letter-spacing: 1px;
            background: transparent;
        ''')
        header.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Nút cài đặt đẹp hơn
        self.btn_settings = QPushButton('⚙️')
        self.btn_settings.setFixedSize(45, 45)
        self.btn_settings.setStyleSheet('''
            QPushButton {
                background: rgba(255, 255, 255, 0.2);
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 22px;
                font-size: 18px;
                color: white;
            }
            QPushButton:hover {
                background: rgba(255, 255, 255, 0.3);
                border: 2px solid rgba(255, 255, 255, 0.5);
            }
            QPushButton:pressed {
                background: rgba(255, 255, 255, 0.1);
            }
        ''')
        self.btn_settings.setToolTip('Cài đặt giao diện')
        self.btn_settings.clicked.connect(self.open_settings_dialog)

        header_layout.addWidget(header)
        header_layout.addWidget(self.btn_settings)
        main_layout.addWidget(header_container)
        # Config Panel với thiết kế card đẹp
        config_frame = QWidget()
        config_frame.setStyleSheet('''
            QWidget {
                background: white;
                border-radius: 12px;
                border: 1px solid #e9ecef;
            }
        ''')
        config_layout = QVBoxLayout(config_frame)
        config_layout.setContentsMargins(15, 12, 15, 12)  # Giảm margins
        config_layout.setSpacing(8)  # Giảm spacing

        # Title cho config
        config_title = QLabel('Cấu hình')
        config_title.setStyleSheet('''
            font-size: 16px;
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
            background: transparent;
        ''')
        config_layout.addWidget(config_title)

        # Row 1: Headless checkbox
        row1_layout = QHBoxLayout()
        self.headless_cb = QCheckBox('Ẩn trình duyệt (headless mode)')
        self.headless_cb.setStyleSheet('''
            QCheckBox {
                font-size: 13px;
                color: #495057;
                spacing: 6px;
                background: transparent;
                padding: 4px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border-radius: 3px;
                border: 2px solid #dee2e6;
                background: white;
            }
            QCheckBox::indicator:checked {
                background: #007bff;
                border: 2px solid #007bff;
            }
            QCheckBox::indicator:hover {
                border: 2px solid #007bff;
                background: #f8f9fa;
            }
            QCheckBox::indicator:checked:hover {
                background: #0056b3;
                border: 2px solid #0056b3;
            }
        ''')
        row1_layout.addWidget(self.headless_cb)
        row1_layout.addStretch()
        config_layout.addLayout(row1_layout)

        # Row 2: Compact responsive layout
        row2_layout = QHBoxLayout()  # Đổi lại thành horizontal nhưng compact hơn
        row2_layout.setSpacing(8)

        # Kích thước compact
        size_label = QLabel('Kích thước:')
        size_label.setStyleSheet('font-size: 13px; color: #495057; font-weight: 500; background: transparent;')
        row2_layout.addWidget(size_label)

        self.width_entry = QLineEdit('600')
        self.width_entry.setFixedWidth(50)  # Giảm width
        self.width_entry.setStyleSheet('''
            QLineEdit {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 2px;
                padding: 2px 4px;
                font-size: 12px;
                color: #495057;
            }
            QLineEdit:focus {
                border: 1px solid #007bff;
                background: white;
            }
        ''')
        row2_layout.addWidget(self.width_entry)

        x_label = QLabel('×')
        x_label.setStyleSheet('font-size: 12px; color: #6c757d; background: transparent;')
        row2_layout.addWidget(x_label)

        self.height_entry = QLineEdit('700')
        self.height_entry.setFixedWidth(50)  # Giảm width
        self.height_entry.setStyleSheet(self.width_entry.styleSheet())
        row2_layout.addWidget(self.height_entry)

        # Separator
        sep_label = QLabel('|')
        sep_label.setStyleSheet('color: #dee2e6; font-size: 12px; background: transparent;')
        row2_layout.addWidget(sep_label)

        # Website compact
        url_label = QLabel('Website:')
        url_label.setStyleSheet('font-size: 13px; color: #495057; font-weight: 500; background: transparent;')
        row2_layout.addWidget(url_label)

        self.url_entry = QLineEdit(self.website_url)
        self.url_entry.setMinimumWidth(120)  # Giảm thêm min width
        self.url_entry.setStyleSheet(self.width_entry.styleSheet())
        row2_layout.addWidget(self.url_entry)

        # Compact buttons
        self.btn_apply = QPushButton('Áp dụng')
        self.btn_apply.setStyleSheet('''
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 2px;
                padding: 2px 6px;
                font-size: 11px;
                font-weight: 600;
                min-width: 50px;
            }
            QPushButton:hover {
                background: #218838;
            }
            QPushButton:pressed {
                background: #1e7e34;
            }
        ''')
        self.btn_apply.clicked.connect(self.apply_url)
        row2_layout.addWidget(self.btn_apply)

        self.btn_clear = QPushButton('Xóa')
        self.btn_clear.setStyleSheet('''
            QPushButton {
                background: #ffc107;
                color: #212529;
                border: none;
                border-radius: 2px;
                padding: 4px 6px;
                font-size: 11px;
                font-weight: 600;
                min-width: 40px;
            }
            QPushButton:hover {
                background: #e0a800;
            }
            QPushButton:pressed {
                background: #d39e00;
            }
        ''')
        self.btn_clear.clicked.connect(self.clear_url)
        row2_layout.addWidget(self.btn_clear)

        row2_layout.addStretch()
        config_layout.addLayout(row2_layout)

        main_layout.addWidget(config_frame)
        # Table với thiết kế đẹp hơn
        table_container = QWidget()
        table_container.setStyleSheet('''
            QWidget {
                background: white;
                border-radius: 12px;
                border: 1px solid #e9ecef;
            }
        ''')
        table_layout = QVBoxLayout(table_container)
        table_layout.setContentsMargins(15, 12, 15, 12)  # Giảm margins
        table_layout.setSpacing(8)  # Giảm spacing

        # Title cho bảng
        table_title = QLabel('Danh sách tài khoản')
        table_title.setStyleSheet('''
            font-size: 16px;
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
            background: transparent;
        ''')
        table_layout.addWidget(table_title)

        self.table = QTableWidget(0, 6)  # 6 cột: STT + Chọn + Data + Hiện MK
        self.table.setHorizontalHeaderLabels(['STT', 'Chọn', 'Tên đăng nhập', 'Mật khẩu', 'Nhân vật', 'Trạng thái'])

        # Thiết lập width cho các cột
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)  # STT cố định
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Fixed)  # Checkbox cố định
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)  # Username stretch
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)  # Password cố định
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Stretch)  # Character stretch
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Stretch)  # Status stretch

        # Thiết lập width cụ thể
        self.table.setColumnWidth(0, 50)   # STT (tăng từ 40 lên 50)
        self.table.setColumnWidth(1, 60)   # Checkbox (tăng từ 50 lên 60)
        self.table.setColumnWidth(3, 140)  # Password (tăng từ 120 lên 140)

        # Cải thiện hiển thị bảng
        self.table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.table.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)
        self.table.setAlternatingRowColors(True)
        self.table.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.table.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # Thiết lập chiều cao hàng
        self.table.verticalHeader().setDefaultSectionSize(45)  # Tăng chiều cao hàng
        self.table.verticalHeader().setVisible(False)  # Ẩn header dọc

        # Style cho bảng
        self.table.setStyleSheet('''
            QTableWidget {
                background: white;
                border: none;
                border-radius: 8px;
                font-size: 13px;
                gridline-color: #e9ecef;
                selection-background-color: #e3f2fd;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                color: white;
                font-weight: 600;
                border: none;
                padding: 12px 8px;
                font-size: 14px;
                border-radius: 0px;
            }
            QHeaderView::section:first {
                border-top-left-radius: 8px;
            }
            QHeaderView::section:last {
                border-top-right-radius: 8px;
            }
            QTableWidget::item {
                padding: 10px 8px;
                border-bottom: 1px solid #f1f3f4;
            }
            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e3f2fd, stop:1 #bbdefb);
                color: #1565c0;
            }
            QTableWidget::item:alternate {
                background: #f8f9fa;
            }
            QScrollBar:vertical {
                background: #f8f9fa;
                width: 12px;
                border-radius: 6px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background: #dee2e6;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: #adb5bd;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
        ''')
        table_layout.addWidget(self.table)
        table_container.setMinimumHeight(300)  # Giảm chiều cao tối thiểu
        main_layout.addWidget(table_container, 1)
        # Action Buttons với thiết kế hiện đại
        btn_container = QWidget()
        btn_container.setStyleSheet('''
            QWidget {
                background: white;
                border-radius: 12px;
                border: 1px solid #e9ecef;
            }
        ''')
        btn_container_layout = QVBoxLayout(btn_container)
        btn_container_layout.setContentsMargins(15, 12, 15, 12)  # Giảm margins
        btn_container_layout.setSpacing(8)  # Giảm spacing

        # Title cho buttons
        btn_title = QLabel('Thao Tác')
        btn_title.setStyleSheet('''
            font-size: 16px;
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
            background: transparent;
        ''')
        btn_container_layout.addWidget(btn_title)

        # Tất cả buttons trong 1 hàng với kích thước đồng đều
        btn_row = QHBoxLayout()
        btn_row.setSpacing(8)

        # Style chung cho tất cả buttons
        button_style = '''
            QPushButton {{
                background: {bg_color};
                color: {text_color};
                border: none;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 13px;
                font-weight: 600;
                min-width: 85px;
                max-width: 85px;
            }}
            QPushButton:hover {{
                background: {hover_color};
            }}
            QPushButton:pressed {{
                background: {pressed_color};
            }}
        '''

        # Thêm button
        self.btn_add = QPushButton('Thêm')
        self.btn_add.setStyleSheet(button_style.format(
            bg_color='#28a745', text_color='white',
            hover_color='#218838', pressed_color='#1e7e34'
        ))
        self.btn_add.clicked.connect(self.add_account)
        btn_row.addWidget(self.btn_add)

        # Sửa button
        self.btn_edit = QPushButton('Sửa')
        self.btn_edit.setStyleSheet(button_style.format(
            bg_color='#007bff', text_color='white',
            hover_color='#0056b3', pressed_color='#004085'
        ))
        self.btn_edit.clicked.connect(self.edit_account)
        btn_row.addWidget(self.btn_edit)

        # Xóa button
        self.btn_delete = QPushButton('Xóa')
        self.btn_delete.setStyleSheet(button_style.format(
            bg_color='#dc3545', text_color='white',
            hover_color='#c82333', pressed_color='#a71e2a'
        ))
        self.btn_delete.clicked.connect(self.delete_account)
        btn_row.addWidget(self.btn_delete)

        # Làm mới button
        self.btn_refresh = QPushButton('Làm mới')
        self.btn_refresh.setStyleSheet(button_style.format(
            bg_color='#6c757d', text_color='white',
            hover_color='#495057', pressed_color='#343a40'
        ))
        self.btn_refresh.clicked.connect(self.refresh_table)
        btn_row.addWidget(self.btn_refresh)

        # Điểm danh button
        self.btn_attend = QPushButton('Điểm danh')
        self.btn_attend.setStyleSheet(button_style.format(
            bg_color='#17a2b8', text_color='white',
            hover_color='#138496', pressed_color='#0f6674'
        ))
        self.btn_attend.clicked.connect(self.on_attendance)
        btn_row.addWidget(self.btn_attend)

        # Dừng button
        self.btn_stop = QPushButton('Dừng')
        self.btn_stop.setStyleSheet(button_style.format(
            bg_color='#ffc107', text_color='#212529',
            hover_color='#e0a800', pressed_color='#d39e00'
        ))
        self.btn_stop.clicked.connect(self.on_stop)
        btn_row.addWidget(self.btn_stop)

        # Sắp xếp button
        self.btn_arrange = QPushButton('Sắp xếp')
        self.btn_arrange.setStyleSheet(button_style.format(
            bg_color='#6f42c1', text_color='white',
            hover_color='#5a32a3', pressed_color='#4c2a85'
        ))
        self.btn_arrange.clicked.connect(self.on_arrange)
        btn_row.addWidget(self.btn_arrange)

        btn_row.addStretch()
        btn_container_layout.addLayout(btn_row)

        main_layout.addWidget(btn_container)
        # Log Panel với thiết kế đẹp
        log_container = QWidget()
        log_container.setStyleSheet('''
            QWidget {
                background: white;
                border-radius: 12px;
                border: 1px solid #e9ecef;
            }
        ''')
        log_layout = QVBoxLayout(log_container)
        log_layout.setContentsMargins(15, 12, 15, 12)  # Giảm margins
        log_layout.setSpacing(8)  # Giảm spacing

        # Title cho log
        log_title = QLabel('Lịch sử hoạt động')
        log_title.setStyleSheet('''
            font-size: 16px;
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
            background: transparent;
        ''')
        log_layout.addWidget(log_title)

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFixedHeight(100)  # Giảm chiều cao
        self.log_text.setStyleSheet('''
            QTextEdit {
                background: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                font-family: "Consolas", "Monaco", "Courier New", monospace;
                font-size: 12px;
                padding: 10px;
                color: #495057;
                line-height: 1.4;
            }
            QTextEdit:focus {
                border: 2px solid #007bff;
                background: white;
            }
            QScrollBar:vertical {
                background: #f8f9fa;
                width: 12px;
                border-radius: 6px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background: #dee2e6;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: #adb5bd;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
        ''')
        self.log_text.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.log_text.setPlaceholderText("Chưa có hoạt động nào được ghi lại...")
        log_layout.addWidget(self.log_text)

        main_layout.addWidget(log_container)

        # Khởi tạo bảng với dữ liệu hiện có
        self.refresh_table()

    def refresh_table(self):
        """Làm mới bảng dữ liệu"""
        # Xóa tất cả dữ liệu cũ
        self.table.setRowCount(0)
        self.check_vars.clear()
        self.checkboxes.clear()
        self.rows.clear()

        # Đọc lại dữ liệu từ file để luôn cập nhật mới nhất
        ACCOUNTS_FILE = 'accounts.json'
        if os.path.exists(ACCOUNTS_FILE):
            with open(ACCOUNTS_FILE, 'r', encoding='utf-8') as f:
                self.accounts[:] = json.load(f)

        # Đọc trạng thái mới nhất từ file log.txt
        log_status = {}
        log_path = 'log.txt'
        if os.path.exists(log_path):
            try:
                with open(log_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        if 'Tài khoản:' in line and 'Kết quả:' in line:
                            parts = line.split('Tài khoản:')
                            if len(parts) > 1:
                                username_part = parts[1].split(' - Kết quả:')
                                if len(username_part) > 1:
                                    username = username_part[0].strip()
                                    result = username_part[1].strip()
                                    log_status[username] = result
            except Exception:
                pass

        # Thêm dữ liệu vào bảng
        for i, acc in enumerate(self.accounts):
            self.table.insertRow(i)

            # STT với thiết kế đẹp và màu sắc
            stt_item = QTableWidgetItem(str(i + 1))
            stt_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            font = stt_item.font()
            font.setBold(True)
            font.setPointSize(11)
            stt_item.setFont(font)
            # Màu nền xen kẽ cho STT
            if i % 2 == 0:
                stt_item.setBackground(QColor('#f8f9fa'))
            else:
                stt_item.setBackground(QColor('#ffffff'))
            stt_item.setForeground(QColor('#495057'))
            self.table.setItem(i, 0, stt_item)

            # Checkbox với thiết kế premium
            checkbox = QCheckBox()
            checkbox.setStyleSheet('''
                QCheckBox {
                    margin: 0px;
                    padding: 0px;
                    spacing: 0px;
                }
                QCheckBox::indicator {
                    width: 18px;
                    height: 18px;
                    border-radius: 4px;
                    border: 2px solid #dee2e6;
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #ffffff, stop:1 #f8f9fa);
                }
                QCheckBox::indicator:hover {
                    border: 2px solid #007bff;
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #e3f2fd, stop:1 #bbdefb);
                }
                QCheckBox::indicator:checked {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #007bff, stop:1 #0056b3);
                    border: 2px solid #007bff;
                    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDNMNC41IDguNUwyIDYiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
                }
                QCheckBox::indicator:checked:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #0056b3, stop:1 #004085);
                    border: 2px solid #0056b3;
                }
            ''')

            # Container cho checkbox với background đẹp
            checkbox_widget = QWidget()
            checkbox_widget.setStyleSheet('''
                QWidget {
                    background: transparent;
                    border-radius: 4px;
                }
                QWidget:hover {
                    background: rgba(0, 123, 255, 0.1);
                }
            ''')
            checkbox_layout = QHBoxLayout(checkbox_widget)
            checkbox_layout.addWidget(checkbox)
            checkbox_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
            checkbox_layout.setContentsMargins(5, 5, 5, 5)
            self.table.setCellWidget(i, 1, checkbox_widget)
            self.checkboxes.append(checkbox)

            # Các cột dữ liệu
            self.table.setItem(i, 2, QTableWidgetItem(acc['username']))

            # Cột mật khẩu với nút hiện/ẩn - thiết kế mới
            password_widget = QWidget()
            password_widget.setStyleSheet('''
                QWidget {
                    background: transparent;
                    margin: 0px;
                    padding: 0px;
                }
            ''')
            password_layout = QHBoxLayout(password_widget)
            password_layout.setContentsMargins(8, 4, 8, 4)
            password_layout.setSpacing(8)

            # Label hiển thị mật khẩu với style đẹp hơn
            masked_password = '●' * min(len(acc['password']), 8)  # Tối đa 8 ký tự
            password_label = QLabel(masked_password)
            password_label.setStyleSheet('''
                QLabel {
                    font-family: "Consolas", "Monaco", monospace;
                    font-size: 12px;
                    color: #495057;
                    background: #f8f9fa;
                    border: 1px solid #e9ecef;
                    border-radius: 4px;
                    padding: 4px 8px;
                    min-width: 60px;
                }
            ''')
            password_layout.addWidget(password_label)

            # Nút hiện/ẩn mật khẩu với thiết kế đẹp hơn
            toggle_btn = QPushButton('👁')
            toggle_btn.setFixedSize(28, 28)
            toggle_btn.setStyleSheet('''
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #ffffff, stop:1 #f8f9fa);
                    border: 1px solid #dee2e6;
                    border-radius: 4px;
                    font-size: 12px;
                    color: #6c757d;
                    padding: 0px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #e3f2fd, stop:1 #bbdefb);
                    border: 1px solid #007bff;
                    color: #007bff;
                }
                QPushButton:pressed {
                    background: #bbdefb;
                    border: 1px solid #0056b3;
                }
            ''')
            toggle_btn.setToolTip('Click để hiện/ẩn mật khẩu')

            # Tạo closure để lưu trữ thông tin
            def make_toggle_function(label, password, btn):
                def toggle():
                    current_text = label.text()
                    if current_text.startswith('●') or current_text.startswith('*'):
                        # Hiện mật khẩu
                        label.setText(password)
                        label.setStyleSheet('''
                            QLabel {
                                font-family: "Consolas", "Monaco", monospace;
                                font-size: 12px;
                                color: #dc3545;
                                background: #fff5f5;
                                border: 1px solid #f5c6cb;
                                border-radius: 1px;
                                padding: 4px 8px;
                                min-width: 60px;
                            }
                        ''')
                        btn.setText('🙈')
                        btn.setToolTip('Click để ẩn mật khẩu')
                    else:
                        # Ẩn mật khẩu
                        masked = '●' * min(len(password), 8)
                        label.setText(masked)
                        label.setStyleSheet('''
                            QLabel {
                                font-family: "Consolas", "Monaco", monospace;
                                font-size: 12px;
                                color: #495057;
                                background: #f8f9fa;
                                border: 1px solid #e9ecef;
                                border-radius: 4px;
                                padding: 4px 8px;
                                min-width: 60px;
                            }
                        ''')
                        btn.setText('👁')
                        btn.setToolTip('Click để hiện mật khẩu')
                return toggle

            toggle_btn.clicked.connect(make_toggle_function(password_label, acc['password'], toggle_btn))
            password_layout.addWidget(toggle_btn)
            password_layout.addStretch()

            self.table.setCellWidget(i, 3, password_widget)

            # Nhân vật và trạng thái
            self.table.setItem(i, 4, QTableWidgetItem(acc['character']))

            # Trạng thái từ log hoặc từ account
            status = log_status.get(acc['username'], acc.get('status', 'Chưa chạy'))
            self.table.setItem(i, 5, QTableWidgetItem(status))

    def get_headless(self):
        """Lấy trạng thái headless"""
        return self.headless_cb.isChecked()

    def get_window_size(self):
        """Lấy kích thước cửa sổ"""
        try:
            width = int(self.width_entry.text())
            height = int(self.height_entry.text())
            return width, height
        except ValueError:
            return 600, 700

    def get_selected_accounts(self):
        """Lấy danh sách tài khoản được chọn"""
        selected = []
        for i, checkbox in enumerate(self.checkboxes):
            if checkbox.isChecked() and i < len(self.accounts):
                selected.append(self.accounts[i]['username'])
        return selected

    def _safe_log(self, message):
        """Thread-safe log method"""
        self.log_text.append(message)
        # Cuộn xuống cuối
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def _safe_refresh(self):
        """Thread-safe refresh method"""
        self.refresh_table()

    def log(self, message):
        """Thêm log vào text area (thread-safe)"""
        self.log_signal.emit(message)

    def safe_refresh_table(self):
        """Làm mới bảng (thread-safe)"""
        self.refresh_signal.emit()

    def add_account(self):
        """Thêm tài khoản mới"""
        dialog = AccountDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            account_data = dialog.get_account_data()
            if account_data:
                self.on_add(account_data)

    def edit_account(self):
        """Sửa tài khoản"""
        current_row = self.table.currentRow()
        if current_row >= 0 and current_row < len(self.accounts):
            account = self.accounts[current_row]
            dialog = AccountDialog(self, account)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                new_account_data = dialog.get_account_data()
                if new_account_data:
                    self.on_edit(account['username'], new_account_data)
        else:
            QMessageBox.warning(self, 'Chú ý', 'Vui lòng chọn tài khoản để sửa!')

    def delete_account(self):
        """Xóa tài khoản"""
        current_row = self.table.currentRow()
        if current_row >= 0 and current_row < len(self.accounts):
            account = self.accounts[current_row]
            reply = QMessageBox.question(self, 'Xác nhận',
                                       f'Bạn có chắc muốn xóa tài khoản "{account["username"]}"?',
                                       QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            if reply == QMessageBox.StandardButton.Yes:
                self.on_delete(account['username'])
        else:
            QMessageBox.warning(self, 'Chú ý', 'Vui lòng chọn tài khoản để xóa!')

    def apply_url(self):
        """Áp dụng URL website"""
        url = self.url_entry.text().strip()
        if url:
            self.on_url_update(url)
            QMessageBox.information(self, 'Thành công', 'Đã cập nhật địa chỉ website!')
        else:
            QMessageBox.warning(self, 'Chú ý', 'URL không được để trống!')

    def clear_url(self):
        """Xóa URL website"""
        self.url_entry.clear()
        self.on_url_update("")
        QMessageBox.information(self, 'Thành công', 'Đã xóa địa chỉ website!')

    def set_arrange_callback(self, callback):
        """Thiết lập callback cho sắp xếp cửa sổ"""
        self.on_arrange = callback

    def set_stop_callback(self, callback):
        """Thiết lập callback cho dừng"""
        self.on_stop = callback

    def open_settings_dialog(self):
        from PyQt6.QtWidgets import QDialog, QVBoxLayout, QLabel, QPushButton, QHBoxLayout, QFrame, QWidget, QSizePolicy
        import json, os
        dlg = QDialog(self)
        dlg.setWindowTitle('Cài đặt giao diện')
        dlg.setMinimumWidth(420)
        layout = QVBoxLayout(dlg)
        layout.setSpacing(14)
        title = QLabel('Chọn bảng màu giao diện')
        title.setStyleSheet('font-size:15px; font-weight:600; color:#22223b; margin-bottom:8px;')
        layout.addWidget(title)
        # Danh sách bảng màu pastel mở rộng
        pastel_palettes = [
            ('Mint Tươi', {'main':'#b2f2bb', 'header':'#a5d8ff', 'btn':'#b7eaff', 'alt':'#e7ecef'}, 'Tươi sáng, năng động'),
            ('Tím Nhạt', {'main':'#e7c6ff', 'header':'#b197fc', 'btn':'#d0bfff', 'alt':'#f3d9fa'}, 'Nhẹ nhàng, nữ tính'),
            ('Vàng Nhạt', {'main':'#fff3bf', 'header':'#ffe066', 'btn':'#ffd6a5', 'alt':'#fff9db'}, 'Ấm áp, thân thiện'),
            ('Hồng Pastel', {'main':'#ffd6e0', 'header':'#fbb1b1', 'btn':'#f9c6c9', 'alt':'#fff0f6'}, 'Ngọt ngào, trẻ trung'),
            ('Xám Sáng', {'main':'#f8f9fa', 'header':'#dee2e6', 'btn':'#ced4da', 'alt':'#e9ecef'}, 'Cổ điển, tối giản'),
            ('Xanh Biển', {'main':'#d0ebff', 'header':'#74c0fc', 'btn':'#a5d8ff', 'alt':'#e3fafc'}, 'Mát mẻ, thư giãn'),
            ('Cam Pastel', {'main':'#ffe5b4', 'header':'#ffd6a5', 'btn':'#ffb4a2', 'alt':'#fff3e6'}, 'Năng lượng, sáng tạo'),
            ('Xanh Lá Nhạt', {'main':'#e9fac8', 'header':'#b2f2bb', 'btn':'#c3fae8', 'alt':'#f4fce3'}, 'Tự nhiên, dịu nhẹ'),
            ('Tím Xanh', {'main':'#cddafd', 'header':'#a5d8ff', 'btn':'#b197fc', 'alt':'#e7ecef'}, 'Hiện đại, sáng tạo'),
            ('Be Pastel', {'main':'#f8edeb', 'header':'#e8cfcf', 'btn':'#f9dcc4', 'alt':'#f6f2f2'}, 'Nhẹ nhàng, sang trọng'),
        ]
        # Hiển thị bảng màu dạng grid
        color_grid = QHBoxLayout()
        color_grid.setSpacing(10)
        self.palette_buttons = []
        for name, palette, desc in pastel_palettes:
            btn = QPushButton()
            btn.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
            btn.setFixedSize(60, 60)
            btn.setStyleSheet(f'background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 {palette["main"]}, stop:1 {palette["header"]}); border-radius:12px; border:2px solid #fff;')
            btn.setToolTip(f'{name}\n{desc}')
            color_grid.addWidget(btn)
            self.palette_buttons.append((btn, palette))
        layout.addLayout(color_grid)
        # Mô tả bảng màu
        self.palette_desc = QLabel('Chọn bảng màu để xem mô tả')
        self.palette_desc.setStyleSheet('font-size:12px; color:#666; margin-top:4px;')
        layout.addWidget(self.palette_desc)
        # Nút lưu
        btn_save = QPushButton('Lưu và áp dụng')
        btn_save.setStyleSheet('background:#b7eaff; color:#22223b; border-radius:8px; padding:8px 18px; font-size:13px; font-weight:600;')
        layout.addWidget(btn_save)
        btn_close = QPushButton('Đóng')
        btn_close.setStyleSheet('background:#e7ecef; color:#22223b; border-radius:8px; padding:8px 18px; font-size:13px;')
        layout.addWidget(btn_close)
        btn_close.clicked.connect(dlg.accept)
        # Xử lý chọn bảng màu
        self.selected_palette = pastel_palettes[0][1]
        def select_palette(idx):
            for i, (btn, _) in enumerate(self.palette_buttons):
                if i == idx:
                    btn.setStyleSheet(btn.styleSheet() + 'border:3px solid #845ef7;')
                else:
                    btn.setStyleSheet(btn.styleSheet().replace('border:3px solid #845ef7;', 'border:2px solid #fff;'))
            self.selected_palette = pastel_palettes[idx][1]
            self.palette_desc.setText(f'<b>{pastel_palettes[idx][0]}</b>: {pastel_palettes[idx][2]}')
        for idx, (btn, _) in enumerate(self.palette_buttons):
            btn.clicked.connect(lambda checked, i=idx: select_palette(i))
        select_palette(0)
        # Lưu và áp dụng
        def save_palette():
            palette = self.selected_palette
            settings_path = os.path.join(os.path.dirname(__file__), '../settings.json')
            with open(settings_path, 'w', encoding='utf-8') as f:
                json.dump(palette, f)
            self.apply_palette(palette)
            dlg.accept()
        btn_save.clicked.connect(save_palette)
        dlg.exec()

    def apply_palette(self, palette):
        # palette: dict với main, header, btn, alt
        self.setStyleSheet(f'background:{palette["alt"]}; font-family:Segoe UI, Roboto, Arial;')
        # Header
        self.findChildren(QLabel)[0].setStyleSheet(f'background:{palette["header"]}; color:#22223b; font-size:20px; font-weight:600; padding:14px 0; border-radius:12px; letter-spacing:1px; margin-bottom:8px;')
        # Config
        self.headless_cb.setStyleSheet(f'QCheckBox {{font-size:13px; color:#22223b; padding:2px;}} QCheckBox::indicator {{width:18px; height:18px; border-radius:9px; border:2px solid {palette["btn"]}; background:#fff;}} QCheckBox::indicator:checked {{background-color:{palette["btn"]}; border:2px solid {palette["header"]};}} QCheckBox::indicator:hover {{border:2px solid {palette["header"]};}}')
        self.width_entry.setStyleSheet(f'background:{palette["main"]}; border-radius:5px; padding:2px; font-size:12px;')
        self.height_entry.setStyleSheet(f'background:{palette["main"]}; border-radius:5px; padding:2px; font-size:12px;')
        self.url_entry.setStyleSheet(f'background:{palette["main"]}; border-radius:5px; padding:2px; font-size:12px;')
        self.btn_apply.setStyleSheet(f'background:{palette["btn"]}; color:#22223b; border-radius:8px; padding:6px 16px; font-size:13px; font-weight:500;')
        self.btn_clear.setStyleSheet(f'background:{palette["header"]}; color:#22223b; border-radius:8px; padding:6px 12px; font-size:13px; font-weight:500;')
        # Table
        self.table.setStyleSheet(f'''
            QTableWidget {{background: #fff; border-radius: 10px; font-size:13px; border: 1px solid {palette["header"]};}}
            QHeaderView::section {{background: {palette["btn"]}; color: #22223b; font-weight: 600; border: none; padding: 8px; font-size:13px; border-radius: 0px;}}
            QTableWidget::item:selected {{background: {palette["main"]}; color: #22223b;}}
            QTableWidget::item {{padding: 6px;}}
            QTableWidget::item:alternate {{background: {palette["alt"]};}}
        ''')

class AccountDialog(QDialog):
    """Dialog để thêm/sửa tài khoản"""
    def __init__(self, parent=None, account=None):
        super().__init__(parent)
        self.account = account
        self.setWindowTitle('Sửa tài khoản' if account else 'Thêm tài khoản')
        self.setMinimumWidth(400)
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(15)

        # Username
        layout.addWidget(QLabel('Tên đăng nhập:'))
        self.username_entry = QLineEdit()
        self.username_entry.setStyleSheet('padding: 8px; border: 1px solid #ddd; border-radius: 5px; font-size: 13px;')
        layout.addWidget(self.username_entry)

        # Password với nút hiện/ẩn
        layout.addWidget(QLabel('Mật khẩu:'))
        password_container = QWidget()
        password_layout = QHBoxLayout(password_container)
        password_layout.setContentsMargins(0, 0, 0, 0)
        password_layout.setSpacing(5)

        self.password_entry = QLineEdit()
        self.password_entry.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_entry.setStyleSheet('padding: 8px; border: 1px solid #ddd; border-radius: 5px; font-size: 13px;')
        password_layout.addWidget(self.password_entry)

        # Nút hiện/ẩn mật khẩu
        self.toggle_password_btn = QPushButton('Hiện')
        self.toggle_password_btn.setFixedSize(45, 35)
        self.toggle_password_btn.setStyleSheet('''
            QPushButton {
                background: #f8f9fa;
                border: 1px solid #ddd;
                border-radius: 5px;
                font-size: 11px;
                font-weight: 600;
                color: #495057;
                padding: 0px;
            }
            QPushButton:hover {
                background: #e9ecef;
                border: 1px solid #007bff;
                color: #007bff;
            }
            QPushButton:pressed {
                background: #dee2e6;
            }
        ''')
        self.toggle_password_btn.setToolTip('Click để hiện/ẩn mật khẩu')
        self.toggle_password_btn.clicked.connect(self.toggle_password_visibility)
        password_layout.addWidget(self.toggle_password_btn)

        layout.addWidget(password_container)

        # Character
        layout.addWidget(QLabel('Nhân vật:'))
        self.character_entry = QLineEdit()
        self.character_entry.setStyleSheet('padding: 8px; border: 1px solid #ddd; border-radius: 5px; font-size: 13px;')
        layout.addWidget(self.character_entry)

        # Buttons
        btn_layout = QHBoxLayout()
        self.btn_ok = QPushButton('Lưu')
        self.btn_ok.setStyleSheet('background: #4CAF50; color: white; padding: 8px 20px; border: none; border-radius: 5px; font-size: 13px; font-weight: bold;')
        self.btn_ok.clicked.connect(self.accept)

        self.btn_cancel = QPushButton('Hủy')
        self.btn_cancel.setStyleSheet('background: #f44336; color: white; padding: 8px 20px; border: none; border-radius: 5px; font-size: 13px; font-weight: bold;')
        self.btn_cancel.clicked.connect(self.reject)

        btn_layout.addWidget(self.btn_ok)
        btn_layout.addWidget(self.btn_cancel)
        layout.addLayout(btn_layout)

        # Nếu đang sửa, điền dữ liệu có sẵn
        if self.account:
            self.username_entry.setText(self.account['username'])
            self.password_entry.setText(self.account['password'])
            self.character_entry.setText(self.account['character'])

    def toggle_password_visibility(self):
        """Chuyển đổi hiện/ẩn mật khẩu"""
        if self.password_entry.echoMode() == QLineEdit.EchoMode.Password:
            # Hiện mật khẩu
            self.password_entry.setEchoMode(QLineEdit.EchoMode.Normal)
            self.toggle_password_btn.setText('Ẩn')
            self.toggle_password_btn.setToolTip('Click để ẩn mật khẩu')
        else:
            # Ẩn mật khẩu
            self.password_entry.setEchoMode(QLineEdit.EchoMode.Password)
            self.toggle_password_btn.setText('Hiện')
            self.toggle_password_btn.setToolTip('Click để hiện mật khẩu')

    def get_account_data(self):
        """Lấy dữ liệu tài khoản từ form"""
        username = self.username_entry.text().strip()
        password = self.password_entry.text().strip()
        character = self.character_entry.text().strip()

        if not username or not password or not character:
            QMessageBox.warning(self, 'Lỗi', 'Vui lòng điền đầy đủ thông tin!')
            return None

        return {
            'username': username,
            'password': password,
            'character': character,
            'status': 'Chưa chạy'
        }

if __name__ == '__main__':
    app = QApplication(sys.argv)
    # Tạo UI demo với dữ liệu giả
    accounts = []
    def dummy_callback(*args): pass
    window = AccountManagerUIQt(accounts, dummy_callback, dummy_callback, dummy_callback,
                               dummy_callback, dummy_callback, "https://ninjaschoolmobile.com/", dummy_callback)
    window.show()
    sys.exit(app.exec())
